<?php

add_filter('manage_edit-product_columns', 'add_custom_product_columns');
function add_custom_product_columns($columns)
{
    // Insert new columns before the "Date" column
    $new_columns = [];
    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        if ($key === 'is_in_stock') {
            $new_columns['stock_us'] = __('Stocks(US)', 'custom-product-importer');
            $new_columns['stock_eu'] = __('Stock(EU)', 'custom-product-importer');
        }
        // Insert new columns after SKU (can change this position)
        if ($key === 'product_cat') {
            $new_columns['brand'] = __('Brand', 'custom-product-importer');
            $new_columns['product_line'] = __('Product Line', 'custom-product-importer');
            $new_columns['product_family'] = __('Product Family', 'custom-product-importer');
            $new_columns['product_series'] = __('Product Series', 'custom-product-importer');
            $new_columns['model_size'] = __('Model/Size', 'custom-product-importer');
            $new_columns['ce_approved'] = __('CE Approved', 'custom-product-importer');
        }
    }
    return $new_columns;
}

add_action('manage_product_posts_custom_column', 'populate_custom_product_columns', 10, 2);
function populate_custom_product_columns($column, $post_id)
{
    switch ($column) {
        case 'stock_us':
            $stock_count = get_post_meta(get_the_ID(), '_stock_us', true); // Retrieve stock count
            $stock_text = $stock_count ? $stock_count : '0';
            $stock_manage = get_post_meta(get_the_ID(), '_manage_stock_us', true);
            if($stock_manage != "yes"){
                $stock_text = "0";
            }
            $stock_text .= ' <mark class="instock">In stock</mark>';
            echo wp_kses_post($stock_text);
            break;
        case 'stock_eu':
            $stock_count = get_post_meta(get_the_ID(), '_stock_eu', true); // Retrieve stock count
            $stock_text = $stock_count ? $stock_count : '0';
            $stock_manage = get_post_meta(get_the_ID(), '_manage_stock_eu', true);
            if($stock_manage != "yes"){
                $stock_text = "0";
            }
            $stock_text .= ' <mark class="instock">In stock</mark>';
            echo wp_kses_post($stock_text);
            break;
        case 'brand':
            echo esc_html(get_post_meta($post_id, '_brand', true));
            break;
        case 'product_line':
            echo esc_html(get_post_meta($post_id, '_product_line', true));
            break;
        case 'product_family':
            echo esc_html(get_post_meta($post_id, '_product_family', true));
            break;
        case 'product_series':
            echo esc_html(get_post_meta($post_id, '_product_series', true));
            break;
        case 'model_size':
            echo esc_html(get_post_meta($post_id, '_model_size', true));
            break;
        case 'ce_approved':
            echo esc_html(get_post_meta($post_id, '_ce_approved', true));
            break;
    }
}

// Make custom columns sortable
add_filter('manage_edit-product_sortable_columns', 'cpi_sortable_columns');
function cpi_sortable_columns($columns)
{
    $columns['stock_us'] = '_stock_us';
    $columns['stock_eu'] = '_stock_eu';
    $columns['brand'] = '_brand';
    $columns['product_line'] = '_product_line';
    $columns['product_family'] = '_product_family';
    $columns['product_series'] = '_product_series';
    $columns['model_size'] = '_model_size';
    $columns['ce_approved'] = '_ce_approved';
    return $columns;
}

add_action('admin_enqueue_scripts', 'enqueue_custom_admin_css');
function enqueue_custom_admin_css()
{
    // Only load on the WooCommerce product edit page
    if ('edit.php' === $GLOBALS['pagenow'] && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
        echo '<style>
            /* Make the table container scrollable */
            .wp-list-table {
                overflow-x: auto; /* Enable horizontal scrolling */
                display: block; /* Allow it to become a block element */
            }

            /* Ensure the table maintains its structure */
            .wp-list-table table {
                width: 100%; /* Full width to utilize horizontal space */
                min-width: 900px; /* Minimum width to trigger scroll, adjust as needed */
            }
            .column-name {
                width: 10% !important; /* Adjust the width of the Model/Size column */
            }
            
            .column-brand {
                width: 9% !important; /* Adjust the width of the Brand column */
            }
            .column-product_line {
                width: 9% !important; /* Adjust the width of the Product Line column */
            }
            .column-product_family {
                width: 9% !important; /* Adjust the width of the Product Family column */
            }
            .column-product_series {
                width: 9% !important; /* Adjust the width of the Product Series column */
            }
            .column-model_size {
                width: 10% !important; /* Adjust the width of the Model/Size column */
            }
        </style>';
    }
}


add_action('woocommerce_product_options_general_product_data', 'add_custom_fields_to_product_edit_page');

function add_custom_fields_to_product_edit_page()
{
    global $post;
    $brand = get_post_meta($post->ID, '_brand', true);
    $product_family = get_post_meta($post->ID, '_product_family', true);
    $product_series = get_post_meta($post->ID, '_product_series', true);
    $product_line = get_post_meta($post->ID, '_product_line', true);
    $model_size = get_post_meta($post->ID, '_model_size', true);
    $ce_approved = get_post_meta($post->ID, '_ce_approved', true);

    error_log(print_r($ce_approved, true));
    // Add a text field for the Brand
    woocommerce_wp_text_input(array(
        'id' => 'brand',
        'label' => __('Brand', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the brand of the product.', 'woocommerce'),
        'value' => $brand
    ));
    // Add a text field for Product Line
    woocommerce_wp_text_input(array(
        'id' => 'product_line',
        'label' => __('Product Line', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the product line.', 'woocommerce'),
        'value' => $product_line
    ));
    // Add a text field for Product Family
    woocommerce_wp_text_input(array(
        'id' => 'product_family',
        'label' => __('Product Family', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the product family.', 'woocommerce'),
        'value' => $product_family
    ));
    // Add a text field for Product Series
    woocommerce_wp_text_input(array(
        'id' => 'product_series',
        'label' => __('Product Series', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the product series.', 'woocommerce'),
        'value' => $product_series
    ));
    // Add a text field for Model/Size
    woocommerce_wp_text_input(array(
        'id' => 'model_size',
        'label' => __('Model/Size', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the model or size of the product.', 'woocommerce'),
        'value' => $model_size
    ));

    woocommerce_wp_checkbox(array(
        'id' => '_ce_approved',
        'label' => __('CE Approved', 'woocommerce'),
        'description' => __('Check if the product is CE approved.', 'woocommerce'),
        'value' => $ce_approved === 'Yes' ? 'yes' : 'no',
        'cbvalue' => 'yes',
        'default' => ($ce_approved === 'Yes' ? 'yes' : 'no')
    ));
}

// Save custom fields
add_action('woocommerce_process_product_meta', 'save_custom_fields');

function save_custom_fields($post_id)
{
    if (isset($_POST['brand'])) {
        update_post_meta($post_id, '_brand', sanitize_text_field($_POST['brand']));
    }
    if (isset($_POST['product_family'])) {
        update_post_meta($post_id, '_product_family', sanitize_text_field($_POST['product_family']));
    }
    if (isset($_POST['product_line'])) {
        update_post_meta($post_id, '_product_line', sanitize_text_field($_POST['product_line']));
    }
    if (isset($_POST['product_series'])) {
        update_post_meta($post_id, '_product_series', sanitize_text_field($_POST['product_series']));
    }
    if (isset($_POST['model_size'])) {
        update_post_meta($post_id, '_model_size', sanitize_text_field($_POST['model_size']));
    }

    $ce_approved_value = isset($_POST['_ce_approved']) ? 'Yes' : 'No';
    update_post_meta($post_id, '_ce_approved', $ce_approved_value);
    
}

// 3 Currencies prices update on product edit page.
add_action('woocommerce_product_options_pricing', 'add_custom_currency_price_fields');

function add_custom_currency_price_fields()
{
    global $post;

    remove_meta_box('_regular_price', 'product', 'normal');
    remove_meta_box('_sale_price', 'product', 'normal');

    $product_sku = get_post_meta($post->ID, '_sku', true);

    $usd_price = get_price_from_table($product_sku, 'price_list');
    $eur_price = get_price_from_table($product_sku, 'price_list_eur');
    $gbp_price = get_price_from_table($product_sku, 'price_list_gbp');

    woocommerce_wp_text_input(array(
        'id' => 'custom_usd_price',
        'label' => __('USD Regular Price', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the USD regular price.', 'woocommerce'),
        'value' => $usd_price['price'],
        'data_type' => 'price',
    ));

    woocommerce_wp_text_input(array(
        'id' => 'custom_eur_price',
        'label' => __('EUR Regular Price', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the EUR regular price.', 'woocommerce'),
        'value' => $eur_price['price'],
        'data_type' => 'price',
    ));

    woocommerce_wp_text_input(array(
        'id' => 'custom_gbp_price',
        'label' => __('GBP Regular Price', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Enter the GBP regular price.', 'woocommerce'),
        'value' => $gbp_price['price'],
        'data_type' => 'price',
    ));
}

add_action('woocommerce_process_product_meta', 'save_custom_currency_prices');

function save_custom_currency_prices($post_id)
{
    $product_sku = get_post_meta($post_id, '_sku', true);

    $usd_price = isset($_POST['custom_usd_price']) ? wc_clean($_POST['custom_usd_price']) : '';
    update_price_in_table($product_sku, 'price_list', $usd_price);

    $eur_price = isset($_POST['custom_eur_price']) ? wc_clean($_POST['custom_eur_price']) : '';
    update_price_in_table($product_sku, 'price_list_eur', $eur_price);

    $gbp_price = isset($_POST['custom_gbp_price']) ? wc_clean($_POST['custom_gbp_price']) : '';
    update_price_in_table($product_sku, 'price_list_gbp', $gbp_price);
}

function get_price_from_table($sku, $tn)
{
    global $wpdb;
    $table_name = $wpdb->prefix . $tn;
    $price_data = $wpdb->get_row($wpdb->prepare(
        "SELECT price FROM {$table_name} WHERE product_sku = %s",
        $sku
    ), ARRAY_A);

    return $price_data ? $price_data : ['price' => ''];
}

function update_price_in_table($sku, $tn, $price)
{
    global $wpdb;
    $table_name = $wpdb->prefix . $tn;
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$table_name} WHERE product_sku = %s",
        $sku
    ));

    if ($exists) {
        $wpdb->update(
            $table_name,
            array('price' => $price),
            array('product_sku' => $sku),
            array('%f'),
            array('%s')
        );
    } else {
        $wpdb->insert(
            $table_name,
            array(
                'product_sku' => $sku,
                'price' => $price
            ),
            array('%s', '%f')
        );
    }
}

// Displaying 3 currencies prices  to woocommerce product table 
add_filter('manage_edit-product_columns', 'add_custom_currency_columns', 15);

function add_custom_currency_columns($columns)
{
    // Add new columns for each currency after the 'price' column
    $columns['usd_price'] = __('USD Price', 'woocommerce');
    $columns['eur_price'] = __('EUR Price', 'woocommerce');
    $columns['gbp_price'] = __('GBP Price', 'woocommerce');

    return $columns;
}

add_action('manage_product_posts_custom_column', 'display_custom_currency_columns', 10, 2);

function display_custom_currency_columns($column, $post_id)
{
    $product_sku = get_post_meta($post_id, '_sku', true);

    switch ($column) {
        case 'usd_price':
            $usd_price = get_price_from_table($product_sku, 'price_list');
            echo isset($usd_price['price']) ? '$' . ($usd_price['price'] != "" ? $usd_price['price'] : "0") : __('N/A', 'woocommerce');
            break;

        case 'eur_price':
            $eur_price = get_price_from_table($product_sku, 'price_list_eur');
            echo isset($eur_price['price']) ? '€' . ($eur_price['price'] != "" ? $eur_price['price'] : "0") : __('N/A', 'woocommerce');
            break;

        case 'gbp_price':
            $gbp_price = get_price_from_table($product_sku, 'price_list_gbp');
            echo isset($gbp_price['price']) ? '£' . ($gbp_price['price'] != "" ? $gbp_price['price'] : "0") : __('N/A', 'woocommerce');
            break;
    }
}

add_filter('manage_edit-product_sortable_columns', 'make_custom_price_columns_sortable');

function make_custom_price_columns_sortable($columns)
{
    $columns['usd_price'] = 'usd_price';
    $columns['eur_price'] = 'eur_price';
    $columns['gbp_price'] = 'gbp_price';

    return $columns;
}

add_action('admin_head', 'custom_currency_column_width');

function custom_currency_column_width()
{
    echo '<style>
        .column-usd_price { width: 8%; }
        .column-eur_price { width: 8%; }
        .column-gbp_price { width: 8%; }
    </style>';
}

// Remove original price field.
add_filter('manage_edit-product_columns', 'remove_default_price_column', 10);

function remove_default_price_column($columns)
{
    unset($columns['price']); // Remove the default price column
    unset($columns['is_in_stock']);
    return $columns;
}

// Move 3 prices field to closer sku
add_filter('manage_edit-product_columns', 'reorder_product_columns', 20);

function reorder_product_columns($columns)
{
    // Remove our custom columns to add them again
    unset($columns['usd_price'], $columns['eur_price'], $columns['gbp_price']);

    // Rebuild the columns array, inserting the price columns after SKU
    $new_columns = array();

    foreach ($columns as $key => $column) {
        $new_columns[$key] = $column;

        // After the 'sku' column, add our custom price columns
        if ($key === 'sku') {
            $new_columns['usd_price'] = __('USD Price', 'woocommerce');
            $new_columns['eur_price'] = __('EUR Price', 'woocommerce');
            $new_columns['gbp_price'] = __('GBP Price', 'woocommerce');
        }
    }

    return $new_columns;
}

// Adding multiple region stock to Product/Inventory tab
add_filter('woocommerce_product_data_tabs', 'customize_inventory_tab_label');
add_action('woocommerce_product_options_inventory_product_data', 'remove_default_stock_fields', 10);

// Add custom fields for US and EU stock management
add_action('woocommerce_product_options_inventory_product_data', 'add_us_eu_stock_fields', 20);
add_action('woocommerce_process_product_meta', 'save_us_eu_stock_fields');

// Disable default stock management fields
function customize_inventory_tab_label($tabs) {
    $tabs['inventory']['label'] = __('Inventory (Custom)', 'woocommerce');
    return $tabs;
}

function remove_default_stock_fields() {
    ?>
    <script>
        jQuery(document).ready(function ($) {
            // Hide original stock fields
            $('#_manage_stock').closest('.options_group').hide();
            $('#_stock').closest('.options_group').hide();
            $('#_backorders').closest('.options_group').hide();
        });
    </script>
    <?php
}

function add_us_eu_stock_fields() {
    global $post;

    echo '<div class="options_group">';

    // US Stock Management
    woocommerce_wp_checkbox(
        array(
            'id'    => '_manage_stock_us',
            'label' => __('Manage Stock (US)', 'woocommerce'),
            'description' => __('Enable stock management for US region.', 'woocommerce'),
            'desc_tip' => true,
        )
    );
    woocommerce_wp_text_input(
        array(
            'id'          => '_stock_us',
            'label'       => __('Stock Quantity (US)', 'woocommerce'),
            'description' => __('Enter stock quantity for the US region.', 'woocommerce'),
            'type'        => 'number',
            'custom_attributes' => array(
                'min' => '0',
                'step' => '1',
            ),
            'desc_tip'    => true,
        )
    );
    woocommerce_wp_select(
        array(
            'id'      => '_backorders_us',
            'label'   => __('Allow Backorders (US)', 'woocommerce'),
            'options' => array(
                'no'     => __('Do not allow', 'woocommerce'),
                'notify' => __('Allow, but notify customer', 'woocommerce'),
                'yes'    => __('Allow', 'woocommerce'),
            ),
        )
    );

    // EU Stock Management
    woocommerce_wp_checkbox(
        array(
            'id'    => '_manage_stock_eu',
            'label' => __('Manage Stock (EU)', 'woocommerce'),
            'description' => __('Enable stock management for EU region.', 'woocommerce'),
            'desc_tip' => true,
        )
    );
    woocommerce_wp_text_input(
        array(
            'id'          => '_stock_eu',
            'label'       => __('Stock Quantity (EU)', 'woocommerce'),
            'description' => __('Enter stock quantity for the EU region.', 'woocommerce'),
            'type'        => 'number',
            'custom_attributes' => array(
                'min' => '0',
                'step' => '1',
            ),
            'desc_tip'    => true,
        )
    );
    woocommerce_wp_select(
        array(
            'id'      => '_backorders_eu',
            'label'   => __('Allow Backorders (EU)', 'woocommerce'),
            'options' => array(
                'no'     => __('Do not allow', 'woocommerce'),
                'notify' => __('Allow, but notify customer', 'woocommerce'),
                'yes'    => __('Allow', 'woocommerce'),
            ),
        )
    );

    echo '</div>';
    ?>
    <script>
        jQuery(document).ready(function ($) {
            function toggleStockFields(region) {
                const manageStock = $(`#_manage_stock_${region}`);
                const stockQuantity = $(`#_stock_${region}`);
                const backorders = $(`#_backorders_${region}`);
                
                if (manageStock.is(':checked')) {
                    stockQuantity.prop('disabled', false);
                    backorders.prop('disabled', false);
                } else {
                    stockQuantity.prop('disabled', true);
                    backorders.prop('disabled', true);
                }
            }

            // Initialize fields on load
            toggleStockFields('us');
            toggleStockFields('eu');

            // Toggle fields on change
            $('#_manage_stock_us').change(function () {
                toggleStockFields('us');
            });
            $('#_manage_stock_eu').change(function () {
                toggleStockFields('eu');
            });
        });
    </script> 
    <?php
}

function save_us_eu_stock_fields($post_id) {
    // US Stock Management
    $manage_stock_us = isset($_POST['_manage_stock_us']) ? 'yes' : 'no';
    update_post_meta($post_id, '_manage_stock_us', $manage_stock_us);

    if (isset($_POST['_stock_us'])) {
        update_post_meta($post_id, '_stock_us', wc_clean($_POST['_stock_us']));
    }

    if (isset($_POST['_backorders_us'])) {
        update_post_meta($post_id, '_backorders_us', wc_clean($_POST['_backorders_us']));
    }

    // EU Stock Management
    $manage_stock_eu = isset($_POST['_manage_stock_eu']) ? 'yes' : 'no';
    update_post_meta($post_id, '_manage_stock_eu', $manage_stock_eu);

    if (isset($_POST['_stock_eu'])) {
        update_post_meta($post_id, '_stock_eu', wc_clean($_POST['_stock_eu']));
    }

    if (isset($_POST['_backorders_eu'])) {
        update_post_meta($post_id, '_backorders_eu', wc_clean($_POST['_backorders_eu']));
    }
}
