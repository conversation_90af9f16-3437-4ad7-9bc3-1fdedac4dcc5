{"mc_conf_version": "2", "ipheader": {"hdr": "HTTP_X_FORWARDED_FOR", "pos": 0}, "brandname": "Bot Protection", "confkey": "41ae39aaf90c9baf1f37df1620320ee3", "fw": {"mode": 3, "reqprofilingmode": 2, "bypasslevel": 2, "cookiemode": 1, "loggingmode": 2, "admincookiemode": 1, "iswpusercookieenabled": true, "wpusercapstoconsider": {"manage_options": 28, "unfiltered_html": 56, "edit_others_posts": 41, "upload_files": 58, "edit_posts": 61, "read": 63, "manage_network": 3, "edit_users": 22, "create_users": 14, "promote_users": 29, "delete_users": 17, "manage_woocommerce": 101}, "wpfruleinitmode": 1, "ipcookiemode": 2, "customroles": [], "cookiekey": "8c987e63b15e7f0b8004144d60457662", "cookiepath": "/", "cookiedomain": "", "rulesmode": 1, "cansetcachepreventioncookie": false, "isgeoblocking": false, "logconfig": {"reqprofilingmode": 2, "loggingmode": 2, "except": {"cookies": ["wordpress_sec_baa0a57c427d5dcc733ece9ff94a4121", "wordpress_logged_in_baa0a57c427d5dcc733ece9ff94a4121", "wordpress_baa0a57c427d5dcc733ece9ff94a4121", "wp-postpass_baa0a57c427d5dcc733ece9ff94a4121", "wp-resetpass-baa0a57c427d5dcc733ece9ff94a4121"], "headers": ["<PERSON><PERSON>"], "post": ["password", "passwd", "pwd"], "json": ["password", "passwd", "pwd"]}, "canlograwbody": true, "logslicesize": 1024}}, "lp": {"ptplug": "malcare", "mode": 3, "captchalimit": 10, "tempblocklimit": 15, "blockalllimit": 100, "failedlogingap": 1800, "successlogingap": 3600, "allblockedgap": 1800}, "reqconfig": {"cangetrawbody": true, "maxrawbodylength": 1000000, "candecodejson": true, "maxjsondecodedepth": 512}, "time": 1722907335}