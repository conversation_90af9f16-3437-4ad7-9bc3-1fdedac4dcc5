<?php

function restrict_checkout_for_roles() {
    // Check if the user is logged in and has a role other than b2b_administrator or b2b_customer
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        $user_roles = $user->roles;
        if (!in_array('b2b_administrator', $user_roles) && !in_array('b2b_customer', $user_roles)) {
            if (is_checkout() && !is_wc_endpoint_url('order-received')) {
                wc_add_notice('You do not have permission to access the checkout page.', 'error');
                wp_safe_redirect(home_url('/my-account'));
                exit;
            }
        }
    }

}
add_action('template_redirect', 'restrict_checkout_for_roles');

function hide_add_to_cart_for_roles($purchasable, $product) {
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        if (!in_array('b2b_administrator', $user->roles) && !in_array('b2b_customer', $user->roles)) {
            return false; // Disable "Add to Cart"
        }
    }
    return $purchasable;
}
add_filter('woocommerce_is_purchasable', 'hide_add_to_cart_for_roles', 10, 2);

// Block wp-admin access for B2B Customers
function restrict_admin_access_for_b2b_customers() {
    // Only run this check in admin area
    if (!is_admin()) {
        return;
    }

    // Allow AJAX requests (needed for frontend functionality)
    if (defined('DOING_AJAX') && DOING_AJAX) {
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        return;
    }

    $user = wp_get_current_user();

    // If user has b2b_customer role but NOT administrator
    if (in_array('b2b_customer', $user->roles) &&
        !in_array('administrator', $user->roles)) {

        // Redirect to My Account page
        wp_safe_redirect(home_url('/my-account'));
        exit;
    }
}
add_action('admin_init', 'restrict_admin_access_for_b2b_customers');

// Block wp-admin access for B2B Administrators (except full administrators)
function restrict_admin_access_for_b2b_administrators() {
    // Only run this check in admin area
    if (!is_admin()) {
        return;
    }

    // Allow AJAX requests (needed for frontend functionality)
    if (defined('DOING_AJAX') && DOING_AJAX) {
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        return;
    }

    $user = wp_get_current_user();

    // If user has b2b_administrator role but NOT full administrator
    if (in_array('b2b_administrator', $user->roles) &&
        !in_array('administrator', $user->roles)) {

        // Log the attempt for security monitoring
        error_log("B2B Administrator (ID: {$user->ID}) attempted to access wp-admin: " . $_SERVER['REQUEST_URI']);

        // Redirect to My Account page
        wp_safe_redirect(home_url('/my-account'));
        exit;
    }
}
add_action('admin_init', 'restrict_admin_access_for_b2b_administrators');

// Hide admin bar for B2B Customers
function hide_admin_bar_for_b2b_customers($show_admin_bar) {
    if (!is_user_logged_in()) {
        return $show_admin_bar;
    }

    $user = wp_get_current_user();

    // Hide admin bar for b2b_customer role (but not for full administrators)
    if (in_array('b2b_customer', $user->roles) &&
        !in_array('administrator', $user->roles)) {
        return false;
    }

    return $show_admin_bar;
}
add_filter('show_admin_bar', 'hide_admin_bar_for_b2b_customers');

// Hide admin bar for B2B Administrators (except full administrators)
function hide_admin_bar_for_b2b_administrators($show_admin_bar) {
    if (!is_user_logged_in()) {
        return $show_admin_bar;
    }

    $user = wp_get_current_user();

    // Hide admin bar for b2b_administrator role (but not for full administrators)
    if (in_array('b2b_administrator', $user->roles) &&
        !in_array('administrator', $user->roles)) {
        return false;
    }

    return $show_admin_bar;
}
add_filter('show_admin_bar', 'hide_admin_bar_for_b2b_administrators');

// Additional security: Block direct admin URL access for B2B Customers
function block_admin_urls_for_b2b_customers() {
    // Check if we're on an admin page (but not AJAX)
    if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
        return;
    }

    if (!is_user_logged_in()) {
        return;
    }

    $user = wp_get_current_user();

    // Block admin access for b2b_customer role
    if (in_array('b2b_customer', $user->roles) &&
        !in_array('administrator', $user->roles)) {

        // Log the attempt for security monitoring
        error_log("B2B Customer (ID: {$user->ID}) attempted to access wp-admin: " . $_SERVER['REQUEST_URI']);

        // Show error message and redirect
        wp_die(
            __('Access Denied: You do not have permission to access the WordPress admin area.', 'wc-b2b'),
            __('Access Denied', 'wc-b2b'),
            array(
                'response' => 403,
                'back_link' => true,
                'text_direction' => 'ltr'
            )
        );
    }
}
add_action('admin_page_access_denied', 'block_admin_urls_for_b2b_customers');
add_action('load-index.php', 'block_admin_urls_for_b2b_customers'); // Dashboard
add_action('load-profile.php', 'block_admin_urls_for_b2b_customers'); // Profile page

// Additional security: Block direct admin URL access for B2B Administrators
function block_admin_urls_for_b2b_administrators() {
    // Check if we're on an admin page (but not AJAX)
    if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
        return;
    }

    if (!is_user_logged_in()) {
        return;
    }

    $user = wp_get_current_user();

    // Block admin access for b2b_administrator role (except full administrators)
    if (in_array('b2b_administrator', $user->roles) &&
        !in_array('administrator', $user->roles)) {

        // Log the attempt for security monitoring
        error_log("B2B Administrator (ID: {$user->ID}) attempted to access wp-admin: " . $_SERVER['REQUEST_URI']);

        // Show error message and redirect
        wp_die(
            __('Access Denied: You do not have permission to access the WordPress admin area.', 'wc-b2b'),
            __('Access Denied', 'wc-b2b'),
            array(
                'response' => 403,
                'back_link' => true,
                'text_direction' => 'ltr'
            )
        );
    }
}
add_action('admin_page_access_denied', 'block_admin_urls_for_b2b_administrators');
add_action('load-index.php', 'block_admin_urls_for_b2b_administrators'); // Dashboard
add_action('load-profile.php', 'block_admin_urls_for_b2b_administrators'); // Profile page

// Block wp-admin access for other B2B roles (engineer, viewer, sales) as well
function restrict_admin_access_for_all_b2b_roles() {
    // Only run this check in admin area
    if (!is_admin()) {
        return;
    }

    // Allow AJAX requests (needed for frontend functionality)
    if (defined('DOING_AJAX') && DOING_AJAX) {
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        return;
    }

    $user = wp_get_current_user();

    // Define B2B roles that should be blocked from wp-admin
    $blocked_b2b_roles = ['b2b_engineer', 'b2b_viewer', 'b2b_sales'];

    // Check if user has any blocked B2B role but NOT full administrator
    $user_has_blocked_role = !empty(array_intersect($blocked_b2b_roles, $user->roles));

    if ($user_has_blocked_role &&
        !in_array('administrator', $user->roles)) {

        // Log the attempt for security monitoring
        error_log("B2B User (ID: {$user->ID}, Roles: " . implode(', ', $user->roles) . ") attempted to access wp-admin: " . $_SERVER['REQUEST_URI']);

        // Redirect to My Account page
        wp_safe_redirect(home_url('/my-account'));
        exit;
    }
}
add_action('admin_init', 'restrict_admin_access_for_all_b2b_roles');

// Hide admin bar for all B2B roles except full administrators
function hide_admin_bar_for_all_b2b_roles($show_admin_bar) {
    if (!is_user_logged_in()) {
        return $show_admin_bar;
    }

    $user = wp_get_current_user();

    // Define B2B roles that should have admin bar hidden
    $b2b_roles = ['b2b_customer', 'b2b_administrator', 'b2b_engineer', 'b2b_viewer', 'b2b_sales'];

    // Check if user has any B2B role but NOT full administrator
    $user_has_b2b_role = !empty(array_intersect($b2b_roles, $user->roles));

    if ($user_has_b2b_role &&
        !in_array('administrator', $user->roles)) {
        return false;
    }

    return $show_admin_bar;
}
add_filter('show_admin_bar', 'hide_admin_bar_for_all_b2b_roles');

function customize_my_account_menu_items($items) {
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        if (in_array('b2b_sales', $user->roles)) {
            unset($items['orders']); // Remove Orders tab
        }
    }
    return $items;
}
add_filter('woocommerce_account_menu_items', 'customize_my_account_menu_items', 10, 1);

