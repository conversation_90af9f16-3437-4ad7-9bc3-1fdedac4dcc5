<?php
function csp_enqueue_scripts_eur()
{
    wp_enqueue_script('jquery');
    wp_enqueue_script('datatable', 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js', array('jquery'), '1.11.5', true);
    wp_enqueue_style('datatable-css', 'https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css');
    wp_enqueue_script('csp-eur-js', plugins_url('/assets/js/csp-eur.js', __FILE__), array('jquery'), '1.0', true);
    wp_enqueue_style('csp-eur-css', plugins_url('/assets/css/csp-eur.css', __FILE__));
    wp_localize_script('csp-eur-js', 'csp_eur_ajax_object', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('csp_eur_nonce')
    ));
}

add_action('admin_enqueue_scripts', 'csp_enqueue_scripts_eur');

// Admin Page HTML
function csp_eur_page()
{
?>
    <div class="wrap">
        <h1 style="margin-bottom: 20px;">Contract Pricing USD</h1>
        <button id="addNewPricingEur" class="button-primary" style="margin-bottom: 20px;">Add New</button>
        <table id="pricingTableEur" class="display">
            <thead>
                <tr>
                    <th>Customer ID</th>
                    <th>Customer Name</th>
                    <th>Product SKU</th>
                    <th>Description</th>
                    <th>Price</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>

    <!-- Add/Edit Modal -->
    <div id="pricingModalEur" style="display: none;">
        <span id="closeModalEur">X</span>
        <form id="pricingFormEur">
            <label for="customer">Customer</label>
            <select id="customerEur" name="customerEur"></select><br>

            <label for="product">Product</label>
            <select id="productEur" name="productEur"></select><br>

            <label for="new_price">Sales Price</label>
            <input type="text" id="new_price_eur" name="new_price_eur" /><br>

            <input type="hidden" id="row_id_eur" name="row_id_eur">
            <div style="display: flex; justify-content:space-between;">
                <button type="submit" class="button-primary btn-submit" id="savePricingEur">Save</button>
                <button type="button" class="button-primary btn-submit" id="closeEur">Close</button>
            </div>
        </form>
    </div>
<?php
}

// AJAX handler to get pricing data
function csp_get_pricing_data_eur()
{
    global $wpdb;
    $table = $wpdb->prefix . 'contract_pricing_eur';
    $results = $wpdb->get_results("SELECT * FROM $table", ARRAY_A);

    $data = array();
    foreach ($results as $row) {
        $data[] = array(
            $row['customer_id'],
            $row['customer_name'],  // Assuming customer_name can be found from wp_users table
            $row['product_sku'],
            $row['description'],
            $row['new_price'],
            '<button class="editPricing" data-id="' . $row['id'] . '">Edit</button> <button class="deletePricing" data-id="' . $row['id'] . '">Delete</button>'
        );
    }

    wp_send_json(array('data' => $data));
}

add_action('wp_ajax_get_pricing_data_eur', 'csp_get_pricing_data_eur');

// AJAX handler to save new pricing
function csp_save_pricing_eur()
{
    global $wpdb;

    $customer_id = $_POST['customerEur'];

    $product_sku = $_POST['productEur'];
    $new_price = $_POST['new_price_eur'];

    // Check if pricing already exists
    $table = $wpdb->prefix . 'contract_pricing_eur';
    $exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM $table WHERE customer_id = %d AND product_sku = %s", $customer_id, $product_sku));

    if ($exists) {
        wp_send_json_error(array('message' => 'Already exist!'));
    } else {
        $users = get_users(array(
            'meta_key' => '_customer',
            'meta_value' => $customer_id,
            'number' => 1 // Limit to 1 result, as we are assuming you want a single user
        ));
        
        // Check if the user is found
        if (!empty($users)) {
            $user = $users[0];
            $disp_name = $user->display_name; // Fetch the display name
            
            // Check if the display name is retrieved
            if($disp_name) {
                $table_price = $wpdb->prefix . 'price_list_eur';
                $products = $wpdb->get_results($wpdb->prepare(
                    "SELECT description FROM $table_price WHERE product_sku = %s", $product_sku
                ), ARRAY_A);
                
                // Check if product exists and description is found
                if (!empty($products)) {
                    $description = $products[0]["description"];
                    
                    // Insert data into the price_list table
                    $inserted = $wpdb->insert($table, array(
                        'customer_id' => $customer_id,
                        'customer_name' => $disp_name,
                        'product_sku' => $product_sku,
                        'description' => $description,
                        'new_price' => $new_price
                    ));
                    if($inserted){
                        wp_send_json_success(["success"=>true]);
                    } else {
                        wp_send_json_success(["success"=>false]);
                    }
                } 
            } 
        } 
        // Insert new data

    }
}

add_action('wp_ajax_save_pricing_eur', 'csp_save_pricing_eur');

// AJAX handler to delete pricing
function csp_delete_pricing_eur()
{
    global $wpdb;
    $row_id = intval($_POST['id']);
    $table = $wpdb->prefix . 'contract_pricing_eur';

    $wpdb->delete($table, array('id' => $row_id));

    wp_send_json_success();
}

add_action('wp_ajax_delete_pricing_eur', 'csp_delete_pricing_eur');

// AJAX handler to get product list for modal dropdown
function csp_get_products_eur()
{
    global $wpdb;
    $cache_key = 'csp_products_eur_cache';
    $cached_products = get_transient($cache_key);
    $products = [];
    if ($cached_products === false) {
        $table = $wpdb->prefix . 'price_list_eur';
        $products = $wpdb->get_results("SELECT product_sku FROM $table", ARRAY_A);

        // Cache the results for 12 hours
        set_transient($cache_key, $products, 12 * HOUR_IN_SECONDS);
    } else {
        $products = $cached_products;
    }
    wp_send_json($products);
}

add_action('wp_ajax_get_products_eur', 'csp_get_products_eur');

// AJAX handler to get customers for modal dropdown
function csp_get_customers_eur()
{
    $cache_key = 'csp_customers_cache';
    $cached_customers = get_transient($cache_key);
    $customer_data = [];

    if ($cached_customers === false) {
        $customers = get_users(array('role__in' => array('customer', 'subscriber')));
        foreach ($customers as $customer) {
            $customer_data[] = array(
                'id' => get_user_meta($customer->ID, '_customer', true),
                'name' => get_userdata($customer->ID)->display_name
            );
        }
        set_transient($cache_key, $customer_data, 12 * HOUR_IN_SECONDS);
    } else {
        $customer_data = $cached_customers;
    }

    wp_send_json($customer_data);
}

add_action('wp_ajax_get_customers_eur', 'csp_get_customers_eur');


function get_pricing_by_id_eur() {
    global $wpdb;

    $id = intval($_POST['id']);
    $table = $wpdb->prefix . 'contract_pricing_eur';
    $pricing = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id), ARRAY_A);
    if ($pricing) {
        wp_send_json_success($pricing);
    } else {
        wp_send_json_error(array('message' => 'No data found for the selected row.'));
    }
    wp_die();
}
add_action('wp_ajax_get_pricing_by_id_eur', 'get_pricing_by_id_eur');

function edit_pricing_eur() {
    global $wpdb;

    // Retrieve the data from the form submission
    $row_id       = intval($_POST['row_id_eur']);  // The ID of the row being edited
    $customer_id  = sanitize_text_field($_POST['customerEur']);  // Customer ID from the dropdown
    $product_sku  = sanitize_text_field($_POST['productEur']);    // Product SKU from the dropdown
    $new_price    = floatval($_POST['new_price_eur']);  // New price value

    // Validate that the required fields are present
    if (empty($row_id) || empty($customer_id) || empty($product_sku) || empty($new_price)) {
        wp_send_json_error(array('message' => 'All fields are required.'));
    }

    // Fetch customer display name based on customer_id (user meta '_customer')
    $users = get_users(array(
        'meta_key'   => '_customer',
        'meta_value' => $customer_id,
        'number'     => 1
    ));

    if (empty($users)) {
        wp_send_json_error(array('message' => 'Invalid customer selected.'));
    }

    $disp_name = $users[0]->display_name;

    // Fetch the product description based on the SKU
    $table_products = $wpdb->prefix . 'price_list_eur';
    $product = $wpdb->get_row($wpdb->prepare("SELECT description FROM $table_products WHERE product_sku = %s", $product_sku), ARRAY_A);

    if (empty($product)) {
        wp_send_json_error(array('message' => 'Invalid product selected.'));
    }

    // Update the data in the contract pricing table
    $table_pricing = $wpdb->prefix . 'contract_pricing_eur';

    $result = $wpdb->update(
        $table_pricing,
        array(
            'customer_id'    => $customer_id,
            'customer_name'  => $disp_name,
            'product_sku'    => $product_sku,
            'description'    => $product['description'],
            'new_price'      => $new_price
        ),
        array('id' => $row_id),  // Where condition to match the row ID
        array('%s', '%s', '%s', '%s', '%f'),
        array('%d')  // Format for the 'id' column
    );

    // Check if the update was successful
    if ($result !== false) {
        wp_send_json_success(array('message' => 'Pricing data updated successfully.'));
    } else {
        wp_send_json_error(array('message' => 'Failed to update pricing data.'));
    }

    wp_die();  // Always call this to terminate the execution properly in AJAX calls
}
add_action('wp_ajax_edit_pricing_eur', 'edit_pricing_eur');