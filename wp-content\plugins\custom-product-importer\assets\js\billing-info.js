jQuery(document).ready(function ($) {
    console.log("This is billing info.")
    $('#btn-billing-excel-file').on('click', function () {
        const fileInput = $('#billing-excel-file')[0];
        if (!fileInput.files.length) {
            alert('Please select an Excel file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = function (e) {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // Process specific sheets
            const sheetsToProcess = ['3090 Sold-to', '4554 Sold-to'];
            let allData = [];

            sheetsToProcess.forEach(sheetName => {
                const sheet = workbook.Sheets[sheetName];
                if (sheet) {
                    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
                    const headers = jsonData.shift(); // Remove header row
                    const validData = jsonData.filter(row => row.length > 0);
                    
                    validData.forEach(row => {
                        allData.push({
                            company_code: row[0] || '',
                            customer_number: row[1] || '',
                            customer_name: row[2] || '',
                            street: row[3] || '',
                            city: row[4] || '',
                            sap_region: row[5] || '',
                            postal_code: row[6] || '',
                            country: row[7] || '',
                            price_group: row[8] || '',
                        });
                    });
                }
            });

            if (allData.length > 0) {
                sendBatchesToServer(allData);
            } else {
                alert('No valid data found in the selected sheets.');
            }
        };

        reader.onerror = function () {
            alert('Failed to read the file.');
        };

        reader.readAsArrayBuffer(fileInput.files[0]);
    });

    function sendBatchesToServer(data) {
        const batchSize = 50; // Send 50 rows per batch
        const totalBatches = Math.ceil(data.length / batchSize);

        $('#billing-upload-pan').show();
        $('#billing-progress-text').text('0%');
        $('#billing-progress-bar').css('width', '0%');

        let currentBatch = 0;
        let currentBatchProgress = 0;
        function processNextBatch() {
            if (currentBatch < totalBatches) {
                const start = currentBatch * batchSize;
                const end = Math.min(start + batchSize, data.length);
                const batch = data.slice(start, end);

                $.ajax({
                    url: ajaxurl, // WordPress AJAX endpoint
                    type: 'POST',
                    data: {
                        action: 'import_billing_info',
                        batch: batch,
                    },
                    success: function (response) {
                        if (response.success) {
                            currentBatch++;
                            currentBatchProgress += batch.length;
                            const percentComplete = (currentBatch / totalBatches) * 100;
                            $('#billing-progress-text').text(percentComplete.toFixed(2) + '%');
                            $('#billing-progress-bar').css('width', percentComplete + '%');
                            processNextBatch();
                        } else {
                            $('#billing-upload-pan').hide();
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function () {
                        $('#billing-upload-pan').hide();
                        alert('An unexpected error occurred.');
                    },
                });
            } else {
                $('#billing-upload-pan').hide();
                alert('Billing information updated successfully.');
            }
        }

        processNextBatch();
    }
});
