jQuery(document).ready(function ($) {
    loadCustomers();
    loadProducts();
    // Load the data using DataTable
    var table = $('#pricingTable').DataTable({
        ajax: {
            url: ajaxurl,
            type: 'POST',
            data: { action: 'get_pricing_data' }
        }
    });

    // Open modal for adding new pricing
    $('#addNewPricing').on('click', function () {
        $('#pricingModal').show();
        $('#row_id').val('');  // Clear row ID for a new entry
        $('#customer').val(''); // Clear customer field
        $('#product').val('');  // Clear product field
        $('#new_price').val(''); // Clear price field
    });

    $('#closeModal').on('click', function () {
        $('#pricingModal').hide();
    });

    // Save pricing data
    $('#pricingForm').on('submit', function (e) {
        e.preventDefault();
        $.post(ajaxurl, $(this).serialize() + '&action=save_pricing', function (response) {
            if (response.success) {
                $('#pricingModal').hide();
                table.ajax.reload();
            } else {
                alert(response.data.message);
            }
        });
    });

    // Load customers for dropdown
    function loadCustomers() {
        $.post(ajaxurl, { action: 'get_customers' }, function (customers) {
            $('#customer').html('');
            $.each(customers, function (i, customer) {
                $('#customer').append('<option value="' + customer.id + '">' + customer.name + '</option>');
            });
        });
    }

    // Load products for dropdown
    function loadProducts() {
        $.post(ajaxurl, { action: 'get_products' }, function (products) {
            $('#product').html('');
            $.each(products, function (i, product) {
                $('#product').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
        });
    }

    // Delete pricing data
    $('#pricingTable').on('click', '.deletePricing', function () {
        if (confirm('Are you sure you want to delete this entry?')) {
            var id = $(this).data('id');
            $.post(ajaxurl, { action: 'delete_pricing', id: id }, function () {
                table.ajax.reload();
            });
        }
    });

    // Edit pricing data)
    $('#pricingTable').on('click', '.editPricing', function () {
        alert("hey!!!");
        var id = $(this).data('id');
        $('#row_id').val(id);
        // You would add code here to load the specific row's data for editing (from server)
    });

});
