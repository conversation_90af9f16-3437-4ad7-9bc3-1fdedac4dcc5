<?php
function csp_enqueue_scripts_gbp()
{
    wp_enqueue_script('jquery');
    wp_enqueue_script('datatable', 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js', array('jquery'), '1.11.5', true);
    wp_enqueue_style('datatable-css', 'https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css');
    wp_enqueue_script('csp-gbp-js', plugins_url('/assets/js/csp-gbp.js', __FILE__), array('jquery'), '1.0', true);
    wp_enqueue_style('csp-gbp-css', plugins_url('/assets/css/csp-gbp.css', __FILE__));
    wp_localize_script('csp-eur-js', 'ajaxurl', admin_url('admin-ajax.php'));
}

add_action('admin_enqueue_scripts', 'csp_enqueue_scripts_gbp');

// Admin Page HTML
function csp_gbp_page()
{
?>
    <div class="wrap">
        <h1 style="margin-bottom: 20px;">Contract Pricing USD</h1>
        <button id="addNewPricingGbp" class="button-primary" style="margin-bottom: 20px;">Add New</button>
        <table id="pricingTableGbp" class="display">
            <thead>
                <tr>
                    <th>Customer ID</th>
                    <th>Customer Name</th>
                    <th>Product SKU</th>
                    <th>Description</th>
                    <th>Price</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>

    <!-- Add/Edit Modal -->
    <div id="pricingModalGbp" style="display: none;">
        <span id="closeModalGbp">X</span>
        <form id="pricingFormGbp">
            <label for="customer">Customer</label>
            <select id="customerGbp" name="customerGbp"></select><br>

            <label for="product">Product</label>
            <select id="productGbp" name="productGbp"></select><br>

            <label for="new_price">Sales Price</label>
            <input type="text" id="new_price_gbp" name="new_price_gbp" /><br>

            <input type="hidden" id="row_id_gbp" name="row_id_gbp">
            <div style="display: flex; justify-content:space-between;">
                <button type="submit" class="button-primary btn-submit" id="savePricingGbp">Save</button>
                <button type="button" class="button-primary btn-submit" id="closeGbp">Close</button>
            </div>
        </form>
    </div>
<?php
}

// AJAX handler to get pricing data
function csp_get_pricing_data_gbp()
{
    global $wpdb;
    $table = $wpdb->prefix . 'contract_pricing_gbp';
    $results = $wpdb->get_results("SELECT * FROM $table", ARRAY_A);

    $data = array();
    foreach ($results as $row) {
        $data[] = array(
            $row['customer_id'],
            $row['customer_name'],  // Assuming customer_name can be found from wp_users table
            $row['product_sku'],
            $row['description'],
            $row['new_price'],
            '<button class="editPricing" data-id="' . $row['id'] . '">Edit</button> <button class="deletePricing" data-id="' . $row['id'] . '">Delete</button>'
        );
    }

    wp_send_json(array('data' => $data));
}

add_action('wp_ajax_get_pricing_data_gbp', 'csp_get_pricing_data_gbp');

// AJAX handler to save new pricing
function csp_save_pricing_gbp()
{
    global $wpdb;

    $customer_id = $_POST['customerGbp'];

    $product_sku = $_POST['productGbp'];
    $new_price = $_POST['new_price_gbp'];

    // Check if pricing already exists
    $table = $wpdb->prefix . 'contract_pricing_gbp';
    $exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM $table WHERE customer_id = %d AND product_sku = %s", $customer_id, $product_sku));

    if ($exists) {
        wp_send_json_error(array('message' => 'Already exist!'));
    } else {
        $users = get_users(array(
            'meta_key' => '_customer',
            'meta_value' => $customer_id,
            'number' => 1 // Limit to 1 result, as we are assuming you want a single user
        ));
        
        // Check if the user is found
        if (!empty($users)) {
            $user = $users[0];
            $disp_name = $user->display_name; // Fetch the display name
            
            // Check if the display name is retrieved
            if($disp_name) {
                $table_price = $wpdb->prefix . 'price_list_gbp';
                $products = $wpdb->get_results($wpdb->prepare(
                    "SELECT description FROM $table_price WHERE product_sku = %s", $product_sku
                ), ARRAY_A);
                
                // Check if product exists and description is found
                if (!empty($products)) {
                    $description = $products[0]["description"];
                    
                    // Insert data into the price_list table
                    $inserted = $wpdb->insert($table, array(
                        'customer_id' => $customer_id,
                        'customer_name' => $disp_name,
                        'product_sku' => $product_sku,
                        'description' => $description,
                        'new_price' => $new_price
                    ));
                    if($inserted){
                        wp_send_json_success(["success"=>true]);
                    } else {
                        wp_send_json_success(["success"=>false]);
                    }
                } 
            } 
        } 
        // Insert new data

    }
}

add_action('wp_ajax_save_pricing_gbp', 'csp_save_pricing_gbp');

// AJAX handler to delete pricing
function csp_delete_pricing_gbp()
{
    global $wpdb;
    $row_id = intval($_POST['id']);
    $table = $wpdb->prefix . 'contract_pricing_gbp';

    $wpdb->delete($table, array('id' => $row_id));

    wp_send_json_success();
}

add_action('wp_ajax_delete_pricing_gbp', 'csp_delete_pricing_gbp');

// AJAX handler to get product list for modal dropdown
function csp_get_products_gbp()
{
    global $wpdb;
    $cache_key = 'csp_products_gbp_cache';
    $cached_products = get_transient($cache_key);
    $products = [];
    if ($cached_products === false) {
        $table = $wpdb->prefix . 'price_list_gbp';
        $products = $wpdb->get_results("SELECT product_sku FROM $table", ARRAY_A);

        // Cache the results for 12 hours
        set_transient($cache_key, $products, 12 * HOUR_IN_SECONDS);
    } else {
        $products = $cached_products;
    }
    wp_send_json($products);
}

add_action('wp_ajax_get_products_gbp', 'csp_get_products_gbp');

// AJAX handler to get customers for modal dropdown
function csp_get_customers_gbp()
{
    $cache_key = 'csp_customers_cache';
    $cached_customers = get_transient($cache_key);
    $customer_data = [];

    if ($cached_customers === false) {
        $customers = get_users(array('role__in' => array('customer', 'subscriber')));
        foreach ($customers as $customer) {
            $customer_data[] = array(
                'id' => get_user_meta($customer->ID, '_customer', true),
                'name' => get_userdata($customer->ID)->display_name
            );
        }
        set_transient($cache_key, $customer_data, 12 * HOUR_IN_SECONDS);
    } else {
        $customer_data = $cached_customers;
    }

    wp_send_json($customer_data);
}

add_action('wp_ajax_get_customers_gbp', 'csp_get_customers_gbp');


function get_pricing_by_id_gbp() {
    global $wpdb;

    $id = intval($_POST['id']);
    $table = $wpdb->prefix . 'contract_pricing_gbp';
    $pricing = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id), ARRAY_A);
    if ($pricing) {
        wp_send_json_success($pricing);
    } else {
        wp_send_json_error(array('message' => 'No data found for the selected row.'));
    }
    wp_die();
}
add_action('wp_ajax_get_pricing_by_id_gbp', 'get_pricing_by_id_gbp');

function edit_pricing_gbp() {
    global $wpdb;

    // Retrieve the data from the form submission
    $row_id       = intval($_POST['row_id_gbp']);  // The ID of the row being edited
    $customer_id  = sanitize_text_field($_POST['customerGbp']);  // Customer ID from the dropdown
    $product_sku  = sanitize_text_field($_POST['productGbp']);    // Product SKU from the dropdown
    $new_price    = floatval($_POST['new_price_gbp']);  // New price value

    // Validate that the required fields are present
    if (empty($row_id) || empty($customer_id) || empty($product_sku) || empty($new_price)) {
        wp_send_json_error(array('message' => 'All fields are required.'));
    }

    // Fetch customer display name based on customer_id (user meta '_customer')
    $users = get_users(array(
        'meta_key'   => '_customer',
        'meta_value' => $customer_id,
        'number'     => 1
    ));

    if (empty($users)) {
        wp_send_json_error(array('message' => 'Invalid customer selected.'));
    }

    $disp_name = $users[0]->display_name;

    // Fetch the product description based on the SKU
    $table_products = $wpdb->prefix . 'price_list_gbp';
    $product = $wpdb->get_row($wpdb->prepare("SELECT description FROM $table_products WHERE product_sku = %s", $product_sku), ARRAY_A);

    if (empty($product)) {
        wp_send_json_error(array('message' => 'Invalid product selected.'));
    }

    // Update the data in the contract pricing table
    $table_pricing = $wpdb->prefix . 'contract_pricing_gbp';

    $result = $wpdb->update(
        $table_pricing,
        array(
            'customer_id'    => $customer_id,
            'customer_name'  => $disp_name,
            'product_sku'    => $product_sku,
            'description'    => $product['description'],
            'new_price'      => $new_price
        ),
        array('id' => $row_id),  // Where condition to match the row ID
        array('%s', '%s', '%s', '%s', '%f'),
        array('%d')  // Format for the 'id' column
    );

    // Check if the update was successful
    if ($result !== false) {
        wp_send_json_success(array('message' => 'Pricing data updated successfully.'));
    } else {
        wp_send_json_error(array('message' => 'Failed to update pricing data.'));
    }

    wp_die();  // Always call this to terminate the execution properly in AJAX calls
}
add_action('wp_ajax_edit_pricing_gbp', 'edit_pricing_gbp');