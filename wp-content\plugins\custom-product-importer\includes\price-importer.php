<?php
function price_code_upload($price_code_list, $region_shuffix)
{
    global $wpdb;
    if (count($price_code_list) > 0) {
        $table_name = $wpdb->prefix . 'price_code_list' . $region_shuffix;
        $new_codes = [];
        foreach ($price_code_list as $price_code) {
            $sql = "SELECT * FROM $table_name WHERE price_code_title='$price_code'";
            $check_result = $wpdb->get_results($sql);
            if (empty($check_result)) {
                $new_codes[] = $price_code;
            }
        }
        if (count($new_codes) > 0) {
            $sql = "INSERT INTO $table_name(price_code_title) VALUES";
            foreach ($new_codes as $new_code) {
                $sql .= "('$new_code'),";
            }
            $sql[strlen($sql) - 1] = ";";
            $result = $wpdb->query($sql);
        }
        $res_array = [];
        $sql = "SELECT * FROM $table_name";
        $codes_result = $wpdb->get_results($sql, ARRAY_A);
        foreach ($codes_result as $row) {
            $res_array[] = $row;
        }
        return $res_array;
    }
}
function price_group_upload($price_group_list, $region_shuffix)
{
    global $wpdb;

    if (count($price_group_list) > 0) {
        $table_name = $wpdb->prefix . 'price_group' . $region_shuffix;
        $new_groups = [];
        foreach ($price_group_list as $price_group) {
            $sql = "SELECT * FROM $table_name WHERE price_group_title='$price_group'";
            $check_result = $wpdb->get_results($sql);
            if (empty($check_result)) {
                $new_groups[] = $price_group;
            }
        }
        if (count($new_groups) > 0) {
            $sql = "INSERT INTO $table_name(price_group_title) VALUES";
            foreach ($new_groups as $new_group) {
                $sql .= "('$new_group'),";
            }
            $sql[strlen($sql) - 1] = ";";
            $result = $wpdb->query($sql);
        }
        $res_array = [];
        $sql = "SELECT * FROM $table_name";
        $codes_result = $wpdb->get_results($sql, ARRAY_A);
        foreach ($codes_result as $row) {
            $res_array[] = $row;
        }
        return $res_array;
    }
}

function price_group_relation($price_group_board, $region_shuffix) {
    global $wpdb;

    $table_name = $wpdb->prefix . "price_code_group_relation" . $region_shuffix;
    if(count($price_group_board)){
        $new_boards = [];
        $update_boards = [];
        $where_boards = [];
        foreach ($price_group_board as $board) {
            $sql = "SELECT * FROM $table_name WHERE price_group_id=$board[1] AND price_code_id=$board[0]";
            $check_result = $wpdb->get_results($sql);
            if (empty($check_result)) {
                $new_boards[] = $board;
            }else{
                $where_boards[] = ['price_code_id' => $board[0], 'price_group_id' => $board[1]];
                $update_boards[] = ['status' => $board[2]];
            }
        }
        if (count($new_boards) > 0) {
            $sql = "INSERT INTO $table_name(price_code_id, price_group_id, status) VALUES";
            foreach ($new_boards as $board) {
                $sql .= "($board[0], $board[1], '$board[2]'),";
            }
            $sql[strlen($sql) - 1] = ";";
            $wpdb->query($sql);
        }

        if(count($update_boards) > 0) {
            foreach($update_boards as $index => $update_board) {
                $wpdb->update( 
                    $table_name, 
                    $update_board, 
                    $where_boards[$index]
                );                
            }
        }
        if ($wpdb->last_error) {
            return false;
        } else {
            return true;
        }

    }
}
function bulk_price_upload($price_data, $region_shuffix)
{
    global $wpdb;

    $table_name = $wpdb->prefix . 'price_list' . $region_shuffix;
    // Check if the price_data is not empty
    if (empty($price_data) || !is_array($price_data)) {
        return;
    }
    // Prepare a placeholder for the query
    $values = [];
    $update_parts = [
        'price_list_value' => [],
        'currency' => [],
        'description' => [],
        'brand' => [],
        'price' => [],
        'price_code_id' => [],
    ];
    $price_sql = "";
    foreach ($price_data as $data) {
        
        // Only add valid data
        if (count($data) < 7) {
            continue; // Skip this entry if there aren't enough fields
        }

        $product_sku = $data[2];
        $product_id = wc_get_product_id_by_sku($product_sku);

        $price_list_value = $data[0];
        $currency = $data[1];
        $description = $data[3];
        $brand = $data[4];
        $price = (float)$data[5];
        $price_code_id = (int)$data[6];
        $values[] = $wpdb->prepare(
            '(%s, %s, %s, %s, %s, %f, %d)',
            $product_sku,
            $price_list_value,
            $currency,
            $description,
            $brand,
            $price,
            $price_code_id
        );
        $update_parts['price_list_value'][] = sprintf(
            'WHEN product_sku = %s THEN %s',
            $wpdb->prepare('%s', $product_sku),
            $wpdb->prepare('%s', $price_list_value)
        );
        $update_parts['currency'][] = sprintf(
            'WHEN product_sku = %s THEN %s',
            $wpdb->prepare('%s', $product_sku),
            $wpdb->prepare('%s', $currency)
        );
        $update_parts['description'][] = sprintf(
            'WHEN product_sku = %s THEN %s',
            $wpdb->prepare('%s', $product_sku),
            $wpdb->prepare('%s', $description)
        );
        $update_parts['brand'][] = sprintf(
            'WHEN product_sku = %s THEN %s',
            $wpdb->prepare('%s', $product_sku),
            $wpdb->prepare('%s', $brand)
        );
        $update_parts['price'][] = sprintf(
            'WHEN product_sku = %s THEN %s',
            $wpdb->prepare('%s', $product_sku),
            $wpdb->prepare('%f', $price)
        );
        $update_parts['price_code_id'][] = sprintf(
            'WHEN product_sku = %s THEN %s',
            $wpdb->prepare('%s', $product_sku),
            $wpdb->prepare('%d', $price_code_id)
        );
    }

    if (empty($values)) {
        return;
    }

    $sql = "INSERT INTO $table_name (product_sku, price_list_value, currency, description, brand, price, price_code_id) VALUES " . implode(', ', $values) . "
    ON DUPLICATE KEY UPDATE 
        price_list_value = CASE " . implode(' ', $update_parts['price_list_value']) . " END,
        currency = CASE " . implode(' ', $update_parts['currency']) . " END,
        description = CASE " . implode(' ', $update_parts['description']) . " END,
        brand = CASE " . implode(' ', $update_parts['brand']) . " END,
        price = CASE " . implode(' ', $update_parts['price']) . " END,
        price_code_id = CASE " . implode(' ', $update_parts['price_code_id']) . " END;
    ";
    $result = $wpdb->query($sql);
    // Check for errors
    if ($result === false) {
        // Error handling: log or display the error message
        return error_log($wpdb->last_error);
    }

    return true; // Indicate success
}
function bulk_contract_price_upload($price_data, $region_shuffix)
{
    global $wpdb;

    $table_name = $wpdb->prefix . 'contract_pricing' . $region_shuffix; 
    // var_dump($table_name);
    // exit;
    if (empty($price_data) || !is_array($price_data)) {
        return;
    }
    $values = [];
    $update_parts = [
        'customer_id' => [],
        'customer_name' => [],
        'product_sku' => [],
        'description' => [],
        'new_price' => [],
        'currency' => [],
    ];

    foreach ($price_data as $data) {
        $id_row = $data[0];
        $customer_id = $data[1];
        $customer_name = $data[2];
        $product_sku = $data[3];
        $description = $data[4];
        $new_price = (float)$data[5];
        $currency = "";
        if (count($data) > 6) {
            $currency = $data[6];
        }
        $values[] = $wpdb->prepare(
            '(%s, %d, %s, %s, %s, %f, %s)',
            $id_row,
            $customer_id,
            $customer_name,
            $product_sku,
            $description,
            $new_price,
            $currency
        );
        $update_parts['customer_id'][] = sprintf(
            'WHEN id_row = %s THEN %s',
            $wpdb->prepare('%s', $id_row),
            $wpdb->prepare('%d', $customer_id)
        );
        $update_parts['customer_name'][] = sprintf(
            'WHEN id_row = %s THEN %s',
            $wpdb->prepare('%s', $id_row),
            $wpdb->prepare('%s', $customer_name)
        );
        $update_parts['product_sku'][] = sprintf(
            'WHEN id_row = %s THEN %s',
            $wpdb->prepare('%s', $id_row),
            $wpdb->prepare('%s', $product_sku)
        );
        $update_parts['description'][] = sprintf(
            'WHEN id_row = %s THEN %s',
            $wpdb->prepare('%s', $id_row),
            $wpdb->prepare('%s', $description)
        );
        $update_parts['new_price'][] = sprintf(
            'WHEN id_row = %s THEN %s',
            $wpdb->prepare('%s', $id_row),
            $wpdb->prepare('%f', $new_price)
        );
        $update_parts['currency'][] = sprintf(
            'WHEN id_row = %s THEN %s',
            $wpdb->prepare('%s', $id_row),
            $wpdb->prepare('%d', $currency)
        );
    }
    if (empty($values)) {
        return;
    }
    $sql = "INSERT INTO $table_name (id_row, customer_id, customer_name, product_sku, description, new_price, currency) VALUES " . implode(', ', $values) . "
    ON DUPLICATE KEY UPDATE 
        customer_id = CASE " . implode(' ', $update_parts['customer_id']) . " END,
        customer_name = CASE " . implode(' ', $update_parts['customer_name']) . " END,
        product_sku = CASE " . implode(' ', $update_parts['product_sku']) . " END,
        description = CASE " . implode(' ', $update_parts['description']) . " END,
        new_price = CASE " . implode(' ', $update_parts['new_price']) . " END,
        currency = CASE " . implode(' ', $update_parts['currency']) . " END;
    ";
    $result = $wpdb->query($sql);
    if ($result === false) {
        return error_log($wpdb->last_error);
    }
    return true; // Indicate success
}

function get_product_sku(){
    // global $wpdb;

    // $table_name = $wpdb->prefix . 'price_list' . $SESSION['region_shuffix'];
    // var_dump($table_name);
    // exit;
    // $sql = "SELECT product_sku FROM $table_name";
    // $check_result = $wpdb->get_results($sql);
    // if (!empty($check_result)) {
    //     return $check_result;
    // }else{
    //     return "failed.";
    // }

}