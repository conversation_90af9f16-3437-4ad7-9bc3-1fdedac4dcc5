<?php
function process_sub_users( $data) {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Unauthorized']);
    }
    foreach ($data as $row) {
        $nameParts = explode(' ', $row['First and last Name'], 2);
        $firstName = $nameParts[0] ?? '';
        $lastName = $nameParts[1] ?? '';
        $email = sanitize_email($row['Email']);
        $customerId = $row['Customer'];
        // For sub users who having country and company codes
        // $companyCode = sanitize_text_field($row['Company Code']);
        // $country = sanitize_text_field($row['Country']);
        
        $billingCompany = sanitize_text_field($row['Customer Name']);

        // Find parent user by _customer field
        $parentUser = get_users([
            'meta_key' => '_customer',
            'meta_value' => $customerId,
            'number' => 1,
            'fields' => 'ID'
        ]);
        if (!empty($parentUser)) {
            $parentId = $parentUser[0];
            // For test sub users
            $company_code = get_user_meta($parentId, '_companycode', true);
            $country = get_user_meta($parentId, '_country', true);
            // Create sub-user
            $userId = wp_insert_user([
                'user_login' => $email,
                'user_email' => $email,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'role' => 'b2b_customer',
                // 'user_pass' => 'QVuWPW2ILdVUuBqE^zXn*qeh',
                'user_pass' => 'Password01!',
            ]);

            if (!is_wp_error($userId)) {
                // Add meta data
                update_user_meta($userId, '_parent_admin_id', $parentId);
                update_user_meta($userId, '_customer', $customerId);
                update_user_meta($userId, '_companycode', $company_code);
                update_user_meta($userId, '_country', $country);
                update_user_meta($userId, 'billing_company', $billingCompany);
            }
        }
    }

    wp_send_json_success(['message' => 'Users processed successfully.']);
}
