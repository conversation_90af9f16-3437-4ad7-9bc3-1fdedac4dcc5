<?php


add_action( "wp_ajax_process_excel_header", "get_header_function" );
add_action( "wp_ajax_bulk_product_upload", "bulk_product_upload" );

function get_header_function() {
    if(isset($_POST["headerData"])) {
        $_SESSION['header_indices'] = array_flip($_POST["headerData"]);
        wp_send_json_success($_SESSION['header_indices']);
    }
    wp_die();
}

function bulk_product_upload(){
    if(isset($_POST['batch_data'])) {
        $tmp_datas = $_POST['batch_data'];
        foreach($tmp_datas as $tmp_data) {
            save_or_update_woocommerce_product($tmp_data);
        }
		//tmp_product_description_upload($tmp_datas);
        wp_send_json_success("success");
    }
    wp_die();
}

// Price ajax
// add_action( "wp_ajax_get_product_sku", "get_product_sku_handler");
add_action( "wp_ajax_price_code_upload", "price_code_upload_handler");
add_action( "wp_ajax_price_group_upload", "price_group_upload_handler");
add_action( "wp_ajax_price_group_relation", "price_group_relation_handler");
add_action( "wp_ajax_process_price_excel_header", "get_price_excel_header" );
add_action( "wp_ajax_bulk_price_upload", "bulk_price_upload_handler" );
add_action( "wp_ajax_contract_price_upload", "contract_price_upload_handler" );
add_action( "wp_ajax_customer_test", "customer_test_handler" );

function price_code_upload_handler(){
    if(isset($_POST['price_code'])){
        $reg_suf ="";
        if($_POST['region_shuffix'] != ""){
            $region_suffix = "_" . $_POST['region_shuffix'];
            $reg_suf = "_" . $_POST['region_shuffix'];
        }else {
            $region_suffix = "";
            $reg_suf = "";
        }

        // Store region suffix in transient instead of session
        $current_user = wp_get_current_user();
        set_transient('region_shuffix_' . $current_user->ID, $region_suffix, HOUR_IN_SECONDS);

        $codes_array = price_code_upload($_POST['price_code'], $reg_suf);
        wp_send_json_success($codes_array);
    }
    wp_die();
}
function get_price_excel_header(){
    if(isset($_POST["headerData"])) {
        $_SESSION['price_header_indices'] = array_flip($_POST["headerData"]);
        wp_send_json_success($_SESSION['price_header_indices']);
    }
    wp_die();
}
function bulk_price_upload_handler(){
    if(isset($_POST["price_data"])) {
        if($_POST['region_shuffix'] != ""){
            $region_suffix = "_" . $_POST['region_shuffix'];
            $reg_suf = "_" . $_POST['region_shuffix'];
        }else {
            $region_suffix = "";
            $reg_suf = "";
        }

        // Store region suffix in transient instead of session
        $current_user = wp_get_current_user();
        set_transient('region_shuffix_' . $current_user->ID, $region_suffix, HOUR_IN_SECONDS);

        $upload_res = bulk_price_upload($_POST["price_data"], $reg_suf);
        wp_send_json_success($upload_res);
    }
    wp_die();
}
function contract_price_upload_handler(){
    if(isset($_POST["cp_data"])) {
        if($_POST['region_shuffix'] != ""){
            $region_suffix = "_" . $_POST['region_shuffix'];
            $reg_suf = "_" . $_POST['region_shuffix'];
        }else {
            $region_suffix = "";
            $reg_suf = "";
        }

        // Store region suffix in transient instead of session
        $current_user = wp_get_current_user();
        set_transient('region_shuffix_' . $current_user->ID, $region_suffix, HOUR_IN_SECONDS);

        $upload_res = bulk_contract_price_upload($_POST["cp_data"], $reg_suf);
        wp_send_json_success($upload_res);
    }
    wp_die();
}

function price_group_upload_handler(){
    if(isset($_POST['price_group'])){
        if($_POST['region_shuffix'] != ""){
            $region_suffix = "_" . $_POST['region_shuffix'];
            $reg_suf = "_" . $_POST['region_shuffix'];
        }else {
            $region_suffix = "";
            $reg_suf = "";
        }

        // Store region suffix in transient instead of session
        $current_user = wp_get_current_user();
        set_transient('region_shuffix_' . $current_user->ID, $region_suffix, HOUR_IN_SECONDS);

        $groups_array = price_group_upload($_POST['price_group'], $reg_suf);
        wp_send_json_success($groups_array);
    }
    wp_die();
}

function price_group_relation_handler(){
    if(isset($_POST['price_group_relation'])){
        if($_POST['region_shuffix'] != ""){
            $region_suffix = "_" . $_POST['region_shuffix'];
            $reg_suf = "_" . $_POST['region_shuffix'];
        }else {
            $region_suffix = "";
            $reg_suf = "";
        }

        // Store region suffix in transient instead of session
        $current_user = wp_get_current_user();
        set_transient('region_shuffix_' . $current_user->ID, $region_suffix, HOUR_IN_SECONDS);

        $result = price_group_relation($_POST['price_group_relation'], $reg_suf);
        wp_send_json_success($result);
    }
    wp_die();
}

function get_product_sku_handler(){
    if(isset($_POST['get_product_sku'])){
        $result = get_product_sku();
        wp_send_json_success($result);
    }
    wp_die();
}

function customer_test_handler(){
    if(isset($_POST['user_name'])){
        $result = get_product_for_customer($_POST['user_name'], $_POST['user_pass']);
        wp_send_json_success($result);
    }
    wp_die();
}

// Stock ajax
// add_action( "wp_ajax_get_product_sku", "get_product_sku_handler");
add_action( "wp_ajax_fetch_product_ids", "fetch_product_ids_handler");
add_action( "wp_ajax_save_stock_data", "save_stock_data_handler");

function fetch_product_ids_handler(){
    if(isset($_POST['skus'])){
        $result = fetch_product_ids($_POST['skus']);
        wp_send_json_success($result);
    }
    wp_die();
}

function save_stock_data_handler(){
    if(isset($_POST['data'])){
        $result = save_stock_data($_POST['data']);
        wp_send_json_success($result);
    }
    wp_die();
}

// Ajax for update billing info.
add_action('wp_ajax_import_billing_info', 'import_billing_info_handler');

function import_billing_info_handler() {
    if (!isset($_POST['batch']) || !is_array($_POST['batch'])) {
        wp_send_json_error('Invalid or missing data batch.');
    } 
	else {
		import_billing_info($_POST['batch']);
	}
	
	wp_die();
}

add_action('wp_ajax_process_sub_users', 'process_sub_users_handler');
function process_sub_users_handler() {
    $sub_user_data = isset($_POST['data']) ? $_POST['data'] : [];
    process_sub_users($sub_user_data);
    // wp_send_json_success(['message' => 'Users processed successfully.']);
    wp_die();
}