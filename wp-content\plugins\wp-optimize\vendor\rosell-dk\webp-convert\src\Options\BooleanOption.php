<?php

namespace WebPConvert\Options;

use WebPConvert\Options\Option;
use WebPConvert\Options\Exceptions\InvalidOptionValueException;

/**
 * Boolean option
 *
 * @package    WebPConvert
 * <AUTHOR> <<EMAIL>>
 * @since      Class available since Release 2.0.0
 */
class BooleanOption extends Option
{

    protected $typeId = 'boolean';
    protected $schemaType = ['boolean'];

    public function check()
    {
        $this->checkType('boolean');
    }

    public function getValueForPrint()
    {
        return ($this->getValue() === true ? 'true' : 'false');
    }
}
