<?php
/**
 * Plugin Name:       Request a Quote for WooCommerce
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
	exit;
}

// Check the installation of WooCommerce module if it is not a multi site.


if (!class_exists('Addify_Request_For_Quote')) {

	class Addify_Request_For_Quote {
	

		/**
		 * Contains an array of quote items.
		 *
		 * @var array
		 */
		public $quote_fields_obj = array();

		public function __construct() {

			$this->afrfq_global_constents_vars();


			add_action('init', array( $this, 'afrfq_plugin_init' ));

			include_once AFRFQ_PLUGIN_DIR . '/includes/post-types/class-af-r-f-q-quote-controller.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/post-types/class-af-r-f-q-rule-controller.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-main.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-quote.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-quote-fields.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-email-controller.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-ajax-controller.php';
			include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-product-addon.php';
			include_once AFRFQ_PLUGIN_DIR . '/af-rfq-general-functions.php';

			if (is_admin()) {
				include_once AFRFQ_PLUGIN_DIR . 'admin/class-af-r-f-q-admin.php';
			} else {
				include_once AFRFQ_PLUGIN_DIR . 'front/class-af-r-f-q-front.php';
			}
		}

		public function afrfq_plugin_init() {

			if (defined('WC_PLUGIN_FILE')) {

				$this->afrfq_custom_post_type();
				$this->include_compatibility_classes();
				add_filter('post_row_actions', array( $this, 'af_rfq_post_link_genrate_pdf' ), 10, 2);

				add_filter('bulk_actions-edit-addify_quote', array( $this, 'af_rfq_add_download_pdf_action_in_bulk_actions' ));

				add_action('admin_action_download_pdf_action', array( $this, 'af_rfq_apply_download_pdf_action' ));

				$this->load_rest_api();
				$this->af_rfq_download_pdf_by_post_link();

			}
		}

		public function load_rest_api() {
			require AFRFQ_PLUGIN_DIR . 'includes/rest-api/class-server.php';
			\Addify\Request_a_Quote\RestApi\Server::instance()->init();
		}

		public function include_compatibility_classes() {

			if (defined('AFB2B_PLUGIN_DIR')) {
				include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-b2b-compatibility.php';
			}

			if (defined('ADDIFY_WSP_PLUGINDIR')) {
				include_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-whole-sale-compatibility.php';
			}
		}

		public function afrfq_global_constents_vars() {

			if (!defined('AFRFQ_URL')) {
				define('AFRFQ_URL', plugin_dir_url(__FILE__));
			}

			if (!defined('AFRFQ_BASENAME')) {
				define('AFRFQ_BASENAME', plugin_basename(__FILE__));
			}

			if (!defined('AFRFQ_PLUGIN_DIR')) {
				define('AFRFQ_PLUGIN_DIR', plugin_dir_path(__FILE__));
			}

			$upload_dir = wp_upload_dir();

			$upload_path = $upload_dir['basedir'] . '/addify-rfq/';

			if (!is_dir($upload_path)) {
				mkdir($upload_path);
			}

			$upload_url = $upload_dir['baseurl'] . '/addify-rfq/';

			define('AFRFQ_UPLOAD_DIR', $upload_path);
			define('AFRFQ_UPLOAD_URL', $upload_url);
		}

		public function afrfq_custom_post_type() {

			$labels = array(
				'name' => esc_html__('Request for Quote Rules', 'addify_b2b'),
				'singular_name' => esc_html__('Request for Quote Rule', 'addify_b2b'),
				'add_new' => esc_html__('Add New Rule', 'addify_b2b'),
				'add_new_item' => esc_html__('Add New Rule', 'addify_b2b'),
				'edit_item' => esc_html__('Edit Rule', 'addify_b2b'),
				'new_item' => esc_html__('New Rule', 'addify_b2b'),
				'view_item' => esc_html__('View Rule', 'addify_b2b'),
				'search_items' => esc_html__('Search Rule', 'addify_b2b'),
				'exclude_from_search' => true,
				'not_found' => esc_html__('No rule found', 'addify_b2b'),
				'not_found_in_trash' => esc_html__('No rule found in trash', 'addify_b2b'),
				'parent_item_colon' => '',
				'menu_name' => esc_html__('Quote Rules', 'addify_b2b'),
				'attributes' => esc_html__('Rule Priority', 'addify_b2b'),
				'item_published' => esc_html__('Quote rule published', 'addify_b2b'),
				'item_updated' => esc_html__('Quote rule updated', 'addify_b2b'),
			);

			$args = array(
				'labels' => $labels,
				'menu_icon' => '',
				'public' => false,
				'publicly_queryable' => false,
				'show_ui' => true,
				'show_in_menu' => 'addify-b2b',
				'query_var' => true,
				'capability_type' => 'post',
				'has_archive' => true,
				'hierarchical' => false,
				'menu_position' => 30,
				'rewrite' => array(
					'slug' => 'addify_rfq',
					'with_front' => false,
				),
				'supports' => array( 'title', 'page-attributes' ),
			);

			register_post_type('addify_rfq', $args);

			$labels = array(
				'name' => esc_html__('Fields for Request a Quote', 'addify_b2b'),
				'singular_name' => esc_html__('Field for Quote Rule', 'addify_b2b'),
				'add_new' => esc_html__('Add New Field', 'addify_b2b'),
				'add_new_item' => esc_html__('Add New Field', 'addify_b2b'),
				'edit_item' => esc_html__('Edit Field', 'addify_b2b'),
				'new_item' => esc_html__('New Field', 'addify_b2b'),
				'view_item' => esc_html__('View Field', 'addify_b2b'),
				'search_items' => esc_html__('Search Field', 'addify_b2b'),
				'exclude_from_search' => true,
				'not_found' => esc_html__('No Field found', 'addify_b2b'),
				'not_found_in_trash' => esc_html__('No Field found in trash', 'addify_b2b'),
				'parent_item_colon' => '',
				'menu_name' => esc_html__('Quote Fields', 'addify_b2b'),
				'attributes' => esc_html__('Field Attributes', 'addify_b2b'),
				'item_published' => esc_html__('Quote field published', 'addify_b2b'),
				'item_updated' => esc_html__('Quote field updated', 'addify_b2b'),
			);

			$args = array(
				'labels' => $labels,
				'menu_icon' => '',
				'public' => false,
				'publicly_queryable' => false,
				'show_ui' => true,
				'show_in_menu' => 'addify-b2b',
				'query_var' => true,
				'capability_type' => 'post',
				'has_archive' => true,
				'hierarchical' => false,
				'menu_position' => 30,
				'rewrite' => array(
					'slug' => 'addify_rfq_fields',
					'with_front' => false,
				),
				'supports' => array( 'title', 'page-attributes' ),
			);

			register_post_type('addify_rfq_fields', $args);

			$labels = array(
				'name' => esc_html__('Quotes', 'addify_b2b'),
				'singular_name' => esc_html__('Quote', 'addify_b2b'),
				'add_new' => esc_html__('New Quote', 'addify_b2b'),
				'add_new_item' => esc_html__('New Quote', 'addify_b2b'),
				'edit_item' => esc_html__('Edit Quote', 'addify_b2b'),
				'new_item' => esc_html__('New Quote', 'addify_b2b'),
				'view_item' => esc_html__('View Quote', 'addify_b2b'),
				'search_items' => esc_html__('Search Quote', 'addify_b2b'),
				'exclude_from_search' => true,
				'not_found' => esc_html__('No Quote found', 'addify_b2b'),
				'not_found_in_trash' => esc_html__('No quote found in trash', 'addify_b2b'),
				'parent_item_colon' => '',
				'menu_name' => esc_html__('All Quotes', 'addify_b2b'),
				'item_published' => esc_html__('Quote published', 'addify_b2b'),
				'item_updated' => esc_html__('Quote updated', 'addify_b2b'),
			);

			$args = array(
				'labels' => $labels,
				'menu_icon' => '',
				'public' => false,
				'publicly_queryable' => false,
				'show_ui' => true,
				'show_in_menu' => 'addify-b2b',
				'query_var' => true,
				'capability_type' => 'post',
				'has_archive' => true,
				'hierarchical' => false,
				'menu_position' => 80,
				'rewrite' => array(
					'slug' => 'addify_quote',
					'with_front' => false,
				),
				'supports' => array( 'title' ),
			);

			register_post_type('addify_quote', $args);
		}



		public function af_rfq_download_pdf_by_post_link() {

			$afrfq_qoute_id_downloadpdf = isset($_GET['af_rfq_download_pdf_with_qoute_id_admin']) ? sanitize_text_field(wp_unslash($_GET['af_rfq_download_pdf_with_qoute_id_admin'])) : '';

			if (!empty($afrfq_qoute_id_downloadpdf)) {

				af_rfq_gernate_pdf_of_order(true, true, false, false, false, false, false, array( $afrfq_qoute_id_downloadpdf ));

			}
		}

		public function af_rfq_post_link_genrate_pdf( $actions, WP_Post $post ) {

			if ('addify_quote' != $post->post_type) {
				return $actions;
			}

			$qoute_id = $post->ID;
			$actions['af_rfq_post_link_genrate_pdf-pdf'] = '<button type="submit" id="af_rfq_download_pdf_with_qoute_id_admin"  name="af_rfq_download_pdf_with_qoute_id_admin" value="' . $qoute_id . '" class="woocommerce-button fas fa-file-pdf af_rfq_download_pdf_with_qoute_id_admin">PDF</button>';

			return $actions;
		}


		public function af_rfq_add_download_pdf_action_in_bulk_actions( $actions ) {

			$afrfq_is_post_type_addify_quote = isset($_REQUEST['post_type']) ? sanitize_text_field(wp_unslash($_REQUEST['post_type'])) : '';

			if (( !empty($afrfq_is_post_type_addify_quote) ) && ( 'addify_quote' == $afrfq_is_post_type_addify_quote )) {

				$actions['download_pdf_action'] = 'Download pdf';

			}

			return $actions;
		}


		public function af_rfq_apply_download_pdf_action() {

			$afrfq_is_action_pdf_bulk_download = isset($_REQUEST['action']) ? sanitize_text_field(wp_unslash($_REQUEST['action'])) : '';

			if ('download_pdf_action' == $afrfq_is_action_pdf_bulk_download) {

				$selected_posts = isset($_REQUEST['post']) ? sanitize_meta('', wp_unslash($_REQUEST['post']), '') : '';

				if (!empty($selected_posts)) {
					af_rfq_gernate_pdf_of_order(true, false, false, true, false, false, false, $selected_posts);
				}

				wp_safe_redirect(admin_url('edit.php?post_type=addify_quote'));

				exit();
			}
		}
	}

	new Addify_Request_For_Quote();

}
