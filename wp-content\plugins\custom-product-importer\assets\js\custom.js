jQuery(document).ready(function ($) {
    console.log("Contract changed to 300")
    const uploadPanel = $('#upload-price-pan');
    let excelData = [];  // To store the parsed Excel data
    let header = [];     // To store the header array
    let file;
    let batchSize = 10;
    var totalProducts = 0;
    var processedProducts = 0;

    let priceExcel = null;
    let listPricingHeader = [];
    let listPricingData = [];
    let listPricingBatch = 10;
    var listPricingCount = 0;
    var totalPrices = 0;
    var processedPrices = 0;
    var contractPricingArray = [];
    var selectedCurrency = "";

    $('#excel-file').on('change', function (e) {
        file = e.target.files[0];
    });

    $('#btn-excel-file').on('click', function () {
        let reader = new FileReader();
        // $('#upload-pan').css({ "display": "flex" });
        // Parse the Excel file
        reader.onload = function (e) {
            let data = new Uint8Array(e.target.result);
            let workbook = XLSX.read(data, { type: 'array' });
            let firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            let excelArray = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
            header = excelArray[0];  // First row is the header
            excelData = excelArray.slice(1); // The rest is data
            totalProducts = excelData.length;
			
            // Now you can send the header array
            sendHeaderToServer(header);
        };
        reader.readAsArrayBuffer(file);

    })

    function sendHeaderToServer(header) {
        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'process_excel_header',
                headerData: header,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                if (response.success) {
                   processProducts(excelData)
                } else {
                    alert('Error in header processing: ' + response.data);
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    function processProducts(productData) {
        var batches = chunkArray(productData, batchSize);

        batches.forEach(function (batch, index) {
            setTimeout(function () {
                uploadBatch(batch, index, batches.length);
            }, index * 7000); // Delay each batch by 1 second
        });
    }

    function uploadBatch(batch, batchIndex, totalBatches) {
        $.ajax({
            url: ajax_obj.ajax_url,
            type: 'POST',
            data: {
                action: 'bulk_product_upload',
                batch_data: batch,
                security: ajax_obj.nonce
            },
            success: function (response) {
                if (response.success) {
                    processedProducts += batch.length;
                    updateProgress();

                    $('#console-log').append('<p>Batch ' + (batchIndex + 1) + ' of ' + totalBatches + ' processed successfully.</p>');
                } else {
                    $('#console-log').append('<p>Error in batch ' + (batchIndex + 1) + '.</p>');
                }
            },
            error: function (xhr, status, error) {
                $('#console-log').append('<p>AJAX error in batch ' + (batchIndex + 1) + ': ' + error + '</p>');
            }
        });
    }

    function updateProgress() {
        var percentComplete = Math.round((processedProducts / totalProducts) * 100);
        console.log("==== percentage complete =====", percentComplete)
        if (percentComplete >= 100) {
            $('#upload-pan').css({ "display": "none" });
            processedProducts = 0;
        }
        $('#progress-bar').css('width', percentComplete + '%');
        $('#progress-text').text(percentComplete + '% complete');
    }

    function chunkArray(array, size) {
        var result = [];
        for (var i = 0; i < array.length; i += size) {
            result.push(array.slice(i, i + size));
        }
        return result;
    }

    $("#import_price_file").on('change', function (e) {
        priceExcel = e.target.files[0];
    })

    $("#btn-import_price_file").on('click', function () {
        if (priceExcel === null) {
            alert("Please selecte price excel file.")
            return;
        }
        var reader = new FileReader();
        reader.onload = function (e) {
            uploadPanel.show();
            var data = new Uint8Array(e.target.result);
            var workbook = XLSX.read(data, { type: 'array' });

            var listPriceItemsSheet = 'List Price Items';
            var listPriceItems = workbook.Sheets[listPriceItemsSheet];
            var listPriceItemsData = XLSX.utils.sheet_to_json(listPriceItems, { header: 1 });
            listPricingHeader = listPriceItemsData[0];
            listPricingData = listPriceItemsData.slice(3);
            totalPrices = listPricingData.length;

            var contractPricingSheet = 'Contract Pricing';
            var contractPricing = workbook.Sheets[contractPricingSheet];
            var contractPricingData = XLSX.utils.sheet_to_json(contractPricing, { header: 1 });
            contractPricingArray = contractPricingData.slice(1);
            totalPrices += contractPricingArray.length;
            var priceGroupCodeSheet = 'Price Groups & Price Code';
            var priceGroupCode = workbook.Sheets[priceGroupCodeSheet];
            var priceGroupCodeData = XLSX.utils.sheet_to_json(priceGroupCode, { header: 1 });

            selectedCurrency = $('input[name="currency"]:checked').val();
            processingPricingCodeGroup(priceGroupCodeData, selectedCurrency);
        };
        reader.readAsArrayBuffer(priceExcel);
    })

    function sendPriceListHeader() {        
        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'process_price_excel_header',
                headerData: listPricingHeader,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                if (response.success) {
                    sendPriceCode(response.data, listPricingData)
                } else {
                    return "no data";
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    function sendPriceCode(headerArray, dataArray) {
        let postIndex = headerArray['Price Code'];
        let priceCodeArray = [];
        let pIndex = 0;

        dataArray.forEach((dataRow, index) => {

            if (!priceCodeArray.includes(dataRow[postIndex])) {
                priceCodeArray[pIndex] = dataRow[postIndex];
                pIndex++;
            }
        })

        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'price_code_upload',
                region_shuffix:selectedCurrency,
                price_code: priceCodeArray,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                console.log(response);
                if (response.success) {
                    sendPriceData(response.data, headerArray, dataArray);
                } else {
                    console.log("Error occured!")
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    function sendPriceData(priceCodeArray, headerArray, dataArray) {
        let priceCodeIndex = headerArray['Price Code'];
        let priceDataArray = [];
        dataArray.forEach((rowData, index) => {
            let newRowData = [];
            newRowData = rowData;
            let tmpCodeIndex = parseInt(getIndexOfPriceCodeArray(rowData[priceCodeIndex], priceCodeArray));
            if(tmpCodeIndex > 0)
            {
                newRowData[priceCodeIndex] = tmpCodeIndex;
                priceDataArray[index] = newRowData;
            }
        })
        if (priceDataArray.length > 0) {
            sendPriceListHandler(priceDataArray);
        }
    }

    function sendPriceListHandler(priceDataArray) {
        var batches = chunkArray(priceDataArray, 300);
        batches.forEach(function (batch, index) {
            setTimeout(function () {
                sendPriceAjaxHandler(batch);
            }, index * 300);
        });
        sendContractPricingData();
    }

    function sendPriceAjaxHandler(priceDataArray) {
        priceDataArray.forEach((e) => {
            if(e[2] == 'PH1002-220'){
                console.log(e)
            }
        })
        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'bulk_price_upload',
                region_shuffix:selectedCurrency,
                price_data: priceDataArray,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                if (response.success) {
                    processedPrices += priceDataArray.length;
                    updatedPriceProgess();
                } else {
                    console.log("Error occured!")
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    
    function updatedPriceProgess() {
        var percentComplete = Math.round((processedPrices / totalPrices) * 100);
        console.log("==== percentage complete =====", percentComplete)
        if (percentComplete >= 100) {
            $('#upload-price-pan').css({ "display": "none" });
            processedPrices = 0;
        }
        $('#price-progress-bar').css('width', percentComplete + '%');
        $('#price-progress-text').text(percentComplete + '% complete');
    }

    function getIndexOfPriceCodeArray(index, priceCodeArray) {

        let codeIndexArray = priceCodeArray.filter((codeItem) => codeItem.price_code_title == index);
        if(codeIndexArray.length > 0) {
            return codeIndexArray[0]['id'];
        }
    }

    function processingPricingCodeGroup(priceGroupCode, selectedCurrency) {
        let pricingCodeHeader = priceGroupCode[0];
        pricingCodeHeader.splice(0, 2);
        sendTotalPriceCode(pricingCodeHeader, priceGroupCode, selectedCurrency);
    }

    function sendTotalPriceCode(priceCodeArray, priceGroupCode, selectedCurrency) {
        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'price_code_upload',
                region_shuffix: selectedCurrency,
                price_code: priceCodeArray,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                if (response.success) {
                    sendGroupCodeHandler(response.data, priceGroupCode);
                } else {
                    console.log("Error occured!")
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    function sendGroupCodeHandler(codeList, groupCodeRelation) {
        let pricingGroup = [];
        groupCodeRelation.slice(1).forEach((el, i) => {
            pricingGroup[i] = el[1];
        })
        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'price_group_upload',
                region_shuffix:selectedCurrency,
                price_group: pricingGroup,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                if (response.success) {
                    adjustCodeGroupBoard(response.data, codeList, groupCodeRelation);
                } else {
                    console.log("Error occured!")
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    function adjustCodeGroupBoard(groupList, codeList, groupCodeRelation) {
        let boardList = [];
        let tmpCodeList = groupCodeRelation[0];
        let tmpBoard = groupCodeRelation.slice(1);
        let index = 0;
        tmpBoard.forEach((boardItems) => {
            if (boardItems.length > 0) {
                let groupId = groupList.find(item => item.price_group_title == boardItems[1].toString());
                boardItems.forEach((boardItem, id) => {
                    if (id > 1) {
                        let tmpRow = [];
                        let codeId = codeList.find(item => item.price_code_title === tmpCodeList[id - 2].toString());
                        tmpRow[0] = codeId.id;
                        tmpRow[1] = groupId.id;
                        tmpRow[2] = boardItem;
                        boardList[index] = tmpRow;
                        if (index % 200 == 0 && index > 1) {
                            sendCodeGroupBoard(boardList);
                            index = 0;
                            boardList = [];
                        } else {
                            index++;
                        }
                    }
                })
            }
        })
        if (index > 0) {
            sendCodeGroupBoard(boardList);
            index = 0;
            boardList = [];
            sendPriceListHeader();
            // sendContractPricingData();
        }
    }

    function sendCodeGroupBoard(boardList) {
        $.ajax({
            url: ajax_obj.ajax_url,  // The AJAX URL from wp_localize_script
            method: 'POST',
            data: {
                action: 'price_group_relation',
                region_shuffix:selectedCurrency,
                price_group_relation: boardList,
                nonce: ajax_obj.nonce  // Nonce for security
            },
            success: function (response) {
                if (response.success) {
                    
                } else {
                    console.log("Error occured!")
                }
            },
            error: function (xhr, status, error) {
                console.log('Header sending error:', xhr.responseText);
            }
        });
    }

    function sendContractPricingData() {
        var batches = chunkArray(contractPricingArray, 300);
        batches.forEach(function (batch, index) {
            setTimeout(function () {
                uploadContractData(batch);
            }, index * 300);
        });
    }

    function uploadContractData(batch) {
        $.ajax({
            url: ajax_obj.ajax_url,
            type: 'POST',
            data: {
                action: 'contract_price_upload',
                region_shuffix:selectedCurrency,
                cp_data: batch,
                security: ajax_obj.nonce
            },
            success: function (response) {
                if (response.success) {
                    processedPrices += batch.length;
                    updatedPriceProgess();
                }
            },
            error: function (xhr, status, error) {
                $('#console-log').append('<p>AJAX error in batch ' + (batchIndex + 1) + ': ' + error + '</p>');
            }
        });
    }

    $("#btn-test-login").on('click', function () {
        if ($("#user-name").val() !== "" && $("#user-pass").val() !== "") {
            $.ajax({
                url: ajax_obj.ajax_url,
                type: 'POST',
                data: {
                    action: 'customer_test',
                    user_name: $("#user-name").val(),
                    user_pass: $("#user-pass").val(),
                    security: ajax_obj.nonce
                },
                success: function (response) {
                    if (response.success) {
                        adjustReceiveData(response.data)
                    }
                },
                error: function (xhr, status, error) {
                    $('#console-log').append('<p>AJAX error in batch : ' + error + '</p>');
                }
            });
        } else {
            alert("Please input correct customer info");
        }
    })

    function adjustReceiveData(totalDataArray) {
        let countryCode = totalDataArray['country_code'];
        let companyCode = totalDataArray['company_code'];
        let customerId = totalDataArray['customer_id'];
        let priceGroup = totalDataArray['price_group'];
        let priceList = totalDataArray['price_list'];
        let currency = totalDataArray['currency'];
        let allowedCodes = totalDataArray['allowed_price_codes'];
        let disallowedCodes = totalDataArray['disallowed_price_codes'];

        $("#country-code").text("Country Code : " + countryCode);
        $("#company-code").text("Company Code : " + companyCode);
        $("#customer-id").text("Customer Id : " + customerId);
        $("#price-group").text("Price Group : " + priceGroup);
        $("#price-list").text("Price List : " + priceList);
        $("#currency").text("Currnecy : " + currency);
        $("#allowed-codes").text("Allowed codes : " + allowedCodes.toString());
        $("#disallowed-codes").text("Disallowed codes : " + disallowedCodes.toString());
        let newPriceProduct = totalDataArray['new_price_products'];
        let allowedProducts = totalDataArray['allowed_products'];

        let html = `
                    <table border="1">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Thumbnail</th>
                                <th>Name</th>
                                <th>SKU</th>
                                <th>Description</th>
                                <th>Quantity in stock</th>
                                <th>List Price</th>
                                <th>Price Code</th>
                                <th>Brand</th>
                                <th>Product Line</th>
                                <th>Product Family</th>
                                <th>Product Series</th>
                                <th>Model/Size</th>
                                <th>CE Approved</th>
                            </tr>
                        </thead>
                        <tbody>
                    `;

        newPriceProduct.forEach((product, index) => {
            html += `
            <tr>
                <td>${index + 1}</td>
                <td>${product.thumbnail}</td>
                <td>${product.name}</td>
                <td>${product.sku}</td>
                <td>${product.description}</td>
                <td>${product.stock}</td>
                <td>${product.price}</td>
                <td>${product.price_code}</td>
                <td>${product.brand}</td>
                <td>${product.product_line}</td>
                <td>${product.product_family}</td>
                <td>${product.product_series}</td>
                <td>${product.model_size}</td>
                <td>${product.ce_approved}</td>
            </tr>
            `
        })
        html += `</tbody></table>`;
        $("#new-products").html(html);
        let html1 = `
                    <table border="1">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Thumbnail</th>
                                <th>Name</th>
                                <th>SKU</th>
                                <th>Description</th>
                                <th>Quantity in stock</th>
                                <th>List Price</th>
                                <th>Price Code</th>
                                <th>Brand</th>
                                <th>Product Line</th>
                                <th>Product Family</th>
                                <th>Product Series</th>
                                <th>Model/Size</th>
                                <th>CE Approved</th>
                            </tr>
                        </thead>
                        <tbody>
                    `;

        allowedProducts.forEach((product, index) => {
            html1 += `
            <tr>
                <td>${index + 1}</td>
                <td>${product.thumbnail}</td>
                <td>${product.name}</td>
                <td>${product.sku}</td>
                <td>${product.description}</td>
                <td>${product.stock}</td>
                <td>${product.price}</td>
                <td>${product.price_code}</td>
                <td>${product.brand}</td>
                <td>${product.product_line}</td>
                <td>${product.product_family}</td>
                <td>${product.product_series}</td>
                <td>${product.model_size}</td>
                <td>${product.ce_approved}</td>
            </tr>
            `
        })
        html1 += `</tbody></table>`;
        $("#map-products").html(html1);
        $("#map-products-count").text("Mapped products (Count : " + allowedProducts.length + ")")

    }
})