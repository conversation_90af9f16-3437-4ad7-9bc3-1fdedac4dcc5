---
title: PHP Simple HTML DOM Parser
---

A fast, simple and reliable HTML document parser for PHP.

Created by <PERSON><PERSON><PERSON><PERSON>, based on [HTML Parser for PHP 4](http://php-html.sourceforge.net/) by <PERSON>.

# Parse any HTML document

PHP Simple HTML DOM Parser handles any HTML document, even ones that are considered
invalid by the [HTML](https://www.w3.org/TR/html/) specification.

# Select elements using CSS selectors

PHP Simple HTML DOM Parser supports CSS style selectors to navigate the DOM,
similar to [jQuery](https://jquery.com/).

# Download

* Download the latest version from [SourceForge](https://sourceforge.net/projects/simplehtmldom/)

# Contributing

* Request features on the [Feature Request Tracker](https://sourceforge.net/p/simplehtmldom/feature-requests/)
* Report bugs on the [Bug Tracker](https://sourceforge.net/p/simplehtmldom/bugs/)
* Get involved with the community on the [Discussions Board](https://sourceforge.net/p/simplehtmldom/discussion/)

# License

PHP Simple HTML DOM Parser is [Free Software](https://en.wikipedia.org/wiki/Free_software)
licensed under the [MIT License](https://opensource.org/licenses/MIT).