<?php
/**
 * Test script for SAP SoldTo database functionality
 * Run this via: wp eval-file wp-content/plugins/sap-soldto/test-database.php
 */

// Load WordPress
require_once dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) . '/wp-load.php';

if ( ! defined( 'ABSPATH' ) ) {
    die( 'WordPress not loaded' );
}

echo "🔍 Testing SAP SoldTo Database Functionality\n";
echo "==========================================\n\n";

// Test 1: Check if table exists
global $wpdb;
$table_name = $wpdb->prefix . 'sap_soldto_customers';

$table_exists = $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) === $table_name;
echo "1. Database Table Check: " . ( $table_exists ? "✅ EXISTS" : "❌ MISSING" ) . "\n";

if ( ! $table_exists ) {
    echo "   Creating table...\n";
    sap_soldto_create_tables();
    $table_exists = $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) === $table_name;
    echo "   After creation: " . ( $table_exists ? "✅ SUCCESS" : "❌ FAILED" ) . "\n";
}

// Test 2: Check table structure
if ( $table_exists ) {
    $columns = $wpdb->get_results( "DESCRIBE $table_name" );
    echo "\n2. Table Structure:\n";
    foreach ( $columns as $column ) {
        echo "   - {$column->Field} ({$column->Type})\n";
    }
}

// Test 3: Test customer manager
echo "\n3. Customer Manager Test:\n";
global $sap_soldto_manager;

if ( ! $sap_soldto_manager ) {
    echo "   ❌ Customer manager not initialized\n";
} else {
    echo "   ✅ Customer manager initialized\n";
    
    // Test data
    $test_data = [
        'customer_id' => 'TEST123456',
        'company_code' => '3090',
        'country_code' => 'US',
        'price_group' => '25',
        'email' => '<EMAIL>',
        'company' => 'Test Company',
        'address_line1' => '123 Test St',
        'city' => 'Test City',
        'postcode' => '12345',
        'country_region' => 'US',
        'state_county' => 'NY',
        'shiptos' => ['SHIP001', 'SHIP002'],
        'status' => 'active'
    ];
    
    // Test insert
    echo "   Testing insert...\n";
    $result = $sap_soldto_manager->save_customer( $test_data );
    
    if ( is_wp_error( $result ) ) {
        echo "   ❌ Insert failed: " . $result->get_error_message() . "\n";
    } else {
        echo "   ✅ Insert successful (ID: $result)\n";
        
        // Test retrieve
        echo "   Testing retrieve...\n";
        $customer = $sap_soldto_manager->get_customer_by_id( 'TEST123456' );
        
        if ( $customer ) {
            echo "   ✅ Retrieve successful\n";
            echo "   - Customer ID: {$customer->customer_id}\n";
            echo "   - Company: {$customer->company}\n";
            echo "   - ShipTos: " . ( is_array( $customer->shiptos ) ? implode( ', ', $customer->shiptos ) : 'None' ) . "\n";
            
            // Test update
            echo "   Testing update...\n";
            $update_data = (array) $customer;
            $update_data['company'] = 'Updated Test Company';
            $update_result = $sap_soldto_manager->save_customer( $update_data );
            
            if ( is_wp_error( $update_result ) ) {
                echo "   ❌ Update failed: " . $update_result->get_error_message() . "\n";
            } else {
                echo "   ✅ Update successful\n";
                
                // Verify update
                $updated_customer = $sap_soldto_manager->get_customer_by_id( 'TEST123456' );
                if ( $updated_customer && $updated_customer->company === 'Updated Test Company' ) {
                    echo "   ✅ Update verified\n";
                } else {
                    echo "   ❌ Update verification failed\n";
                }
            }
            
            // Test delete
            echo "   Testing delete...\n";
            $delete_result = $sap_soldto_manager->delete_customer( 'TEST123456' );
            
            if ( $delete_result ) {
                echo "   ✅ Delete successful\n";
                
                // Verify delete
                $deleted_customer = $sap_soldto_manager->get_customer_by_id( 'TEST123456' );
                if ( ! $deleted_customer ) {
                    echo "   ✅ Delete verified\n";
                } else {
                    echo "   ❌ Delete verification failed\n";
                }
            } else {
                echo "   ❌ Delete failed\n";
            }
            
        } else {
            echo "   ❌ Retrieve failed\n";
        }
    }
}

// Test 4: Helper functions
echo "\n4. Helper Functions Test:\n";

$available = sap_soldto_get_available_customer_ids( 5 );
echo "   Available customer IDs: " . count( $available ) . "\n";

$validation = sap_soldto_validate_customer_id( 'NONEXISTENT' );
if ( is_wp_error( $validation ) ) {
    echo "   ✅ Validation correctly rejects invalid customer ID\n";
} else {
    echo "   ❌ Validation should reject invalid customer ID\n";
}

echo "\n🎉 Database functionality test completed!\n";
