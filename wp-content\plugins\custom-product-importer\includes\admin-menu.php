<?php

function add_main_menus()
{
    add_menu_page(
        'Importer',              // Page title
        'Importer',              // Menu title
        'manage_options',                          // Capability
        'import-product-price',                         // Menu slug
        'import_products_page',                    // Function to display content
        'dashicons-upload',                        // Menu icon
        56                                         // Position
    );
}

function add_import_menus()
{
    // Add "Import Products" menu
    add_submenu_page(
        'import-product-price',
        'Product',              // Page title
        'Product',              // Menu title
        'manage_options',                          // Capability
        'import-products',                         // Menu slug
        'import_products_page',                    // Function to display content
        56                                         // Position
    );

    // Add "Import Prices" menu
    add_submenu_page(
        'import-product-price',
        'Price',              // Page title
        'Price',              // Menu title
        'manage_options',                          // Capability
        'import-prices',                           // Menu slug
        'import_prices_page',                      // Function to display content
        56                                         // Position
    );
	
    add_submenu_page(
        'import-product-price',
        'Stock',              // Page title
        'Stock',              // Menu title
        'manage_options',                          // Capability
        'import-stocks',                           // Menu slug
        'import_stocks_page',                      // Function to display content
        56                                         // Position
    );
	
    add_submenu_page(
        'import-product-price',
        'Billing-Info',              // Page title
        'Billing info',              // Menu title
        'manage_options',                          // Capability
        'import-billing',                           // Menu slug
        'import_billing_info_page',                      // Function to display content
        56                                         // Position
    );

    add_submenu_page(
        'import-product-price',
        'Sub User Bulk Upload',              // Page title
        'Sub User Bulk Upload',              // Menu title
        'manage_options',                          // Capability
        'import-subuser',                           // Menu slug
        'import_sub_user_page',                      // Function to display content
        56                                         // Position
    );

    // add_submenu_page(
    //     'import-product-price',
    //     'Contract Pricing',
    //     'Pricing-USD',
    //     'manage_options',
    //     'customer-special-pricing',
    //     'csp_admin_page',                      // Menu icon
    //     56
    // );
    // add_submenu_page(
    //     'import-product-price',
    //     'Register',              // Page title
    //     'Register',              // Menu title
    //     'manage_options',                          // Capability
    //     'test-register',                           // Menu slug
    //     'customer_register',                      // Function to display content
    //     56                                         // Position
    // );
    // add_submenu_page(
    //     'import-product-price',
    //     'Test Customer',              // Page title
    //     'Test Customer',              // Menu title
    //     'manage_options',                          // Capability
    //     'test-customer',                           // Menu slug
    //     'customer_test',                      // Function to display content
    //     56                                         // Position
    // );
    remove_submenu_page('import-product-price', 'import-product-price');
}

add_action('admin_menu', 'add_main_menus');
add_action('admin_menu', 'add_import_menus');



function import_products_page()
{
?>
    <div class="wrap">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
        <h1>Import Products</h1>
        <form method="post" enctype="multipart/form-data" style="position: relative;">
            <div id="upload-pan" style="position: absolute; top:0px; opacity:50%; display:none; align-items:center; justify-content:center; color:white; left:0px; width: 100%; height:100%; background-color:black;">
                <p>Uploading started. Don't refresh page.</p>
            </div>
            <input type="file" name="import_products_file" id="excel-file" accept=".xlsx" required />
            <input type="button" name="import_products_file" id="btn-excel-file" value="Import Products" class="button button-primary" />
        </form>
        <div class="progress" style="text-align: center;">
            <p id="progress-text">0%</p>
            <div id="progress-bar" style="width: 0%; height: 5px; background-color: green; color:white; margin-top: 10px; border-radius: 5px">
            </div>
        </div>
    </div>
<?php
}

function import_prices_page()
{
?>
    <div class="wrap">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
        <h1>Import Prices</h1>
        <form method="post" enctype="multipart/form-data" style="position: relative;">
            <div id="upload-price-pan" style="position: absolute; top:0px; text-align:center; opacity:50%; display:none; align-items:center; justify-content:center; color:white; left:0px; width: 100%; height:100%; background-color:black;">
                <p>Uploading started. Don't refresh page.</p>
            </div>
            <input type="file" name="import_price_file" id="import_price_file" accept=".xlsx" required />
            <br><br>
            <label>Select Currency:</label>
            <input type="radio" name="currency" value="" id="currency-usd" checked> USD
            <input type="radio" name="currency" value="eur" id="currency-eur"> EUR
            <input type="radio" name="currency" value="gbp" id="currency-gbp"> GBP
            <br><br>
            <input type="button" name="btn-import_price_file" id="btn-import_price_file" value="Import Prices" class="button button-primary" />
        </form>
        <div class="progress" style="text-align: center;">
            <p id="price-progress-text">0%</p>
            <div id="price-progress-bar" style="width: 0%; height: 5px; background-color: green; color:white; margin-top: 10px; border-radius: 5px">
            </div>
        </div>
    </div>
<?php
}


function import_stocks_page()
{
?>
    <div class="wrap">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
        <h1>Import Stocks</h1>
        <form method="post" enctype="multipart/form-data" style="position: relative;">
            <div id="stock-upload-pan" style="position: absolute; top:0px; opacity:50%; display:none; align-items:center; justify-content:center; color:white; left:0px; width: 100%; height:100%; background-color:black;">
                <p>Uploading started. Don't refresh page.</p>
            </div>
            <input type="file" name="import_stock_file" id="stock-excel-file" accept=".xlsx" required />
            <input type="button" id="btn-stock-excel-file" value="Import Stocks" class="button button-primary" />
        </form>
        <div class="progress" style="text-align: center;">
            <p id="stock-progress-text">0%</p>
            <div id="stock-progress-bar" style="width: 0%; height: 5px; background-color: green; color:white; margin-top: 10px; border-radius: 5px">
            </div>
        </div>
    </div>
<?php
}



function import_billing_info_page()
{
?>
    <div class="wrap">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
        <h1>Import Billing Information</h1>
        <form method="post" enctype="multipart/form-data" style="position: relative;">
            <div id="billing-upload-pan" style="position: absolute; top:0px; opacity:50%; display:none; align-items:center; justify-content:center; color:white; left:0px; width: 100%; height:100%; background-color:black;">
                <p>Uploading started. Don't refresh page.</p>
            </div>
            <input type="file" name="import_billing_file" id="billing-excel-file" accept=".xlsx" required />
            <input type="button" id="btn-billing-excel-file" value="Import Billing Info" class="button button-primary" />
        </form>
        <div class="progress" style="text-align: center;">
            <p id="billing-progress-text">0%</p>
            <div id="billing-progress-bar" style="width: 0%; height: 5px; background-color: green; color:white; margin-top: 10px; border-radius: 5px">
            </div>
        </div>
    </div>
<?php
}

function import_sub_user_page()
{
?>
    <div class="wrap">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
        <h1>Import Sub User Information</h1>
        <form method="post" enctype="multipart/form-data" style="position: relative;">
            <div id="subuser-upload-pan" style="position: absolute; top:0px; opacity:50%; display:none; align-items:center; justify-content:center; color:white; left:0px; width: 100%; height:100%; background-color:black;">
                <p>Uploading started. Don't refresh page.</p>
            </div>
            <input type="file" name="import_subuser_file" id="subuser-excel-file" accept=".xlsx" required />
            <input type="button" id="btn-subuser-excel-file" value="Import Sub User Info" class="button button-primary" />
        </form>
        <div class="progress" style="text-align: center;">
            <p id="subuser-progress-text">0%</p>
            <div id="subuser-progress-bar" style="width: 0%; height: 5px; background-color: green; color:white; margin-top: 10px; border-radius: 5px">
            </div>
        </div>
    </div>
<?php
}

function customer_register()
{
?>
    <div class="wrap">
        <h1>Register</h1>
        <form id="registration-form" action="" method="POST" style="width:200px;">
            <input type="text" name="username" placeholder="Username" style="margin-bottom:5px;" required>
            <input type="email" name="email" placeholder="Email" style="margin-bottom:5px;" required>
            <input type="password" name="password" placeholder="Password" style="margin-bottom:5px;" required>

            <!-- Custom Meta Fields -->
            <input type="text" name="country_code" placeholder="Country code" style="margin-bottom:5px;" required>
            <input type="text" name="company_code" placeholder="Company code" style="margin-bottom:5px;" required>
            <input type="text" name="temp_customer_discount_id" placeholder="Temp customer Special/Discount Id" style="margin-bottom:5px;" required>
            <input type="text" name="price_group" placeholder="Price group" style="margin-bottom:5px;" required>
            <input type="submit" name="register" value="Register">
        </form>
    </div>
<?php
}

add_shortcode('custom_registration_form', 'customer_register');


function custom_user_registration()
{
    if (isset($_POST['register'])) {
        $username = sanitize_user($_POST['username']);
        $email = sanitize_email($_POST['email']);
        $password = sanitize_text_field($_POST['password']);
        $country_code = sanitize_text_field($_POST['country_code']);
        $company_code = sanitize_text_field($_POST['company_code']);
        $price_group = sanitize_text_field($_POST['price_group']);
        $tmp_customer_id = sanitize_text_field($_POST['temp_customer_discount_id']);

        // Validate form inputs
        if (username_exists($username) || email_exists($email)) {
            echo 'Username or email already exists!';
            return;
        }

        // Register the user
        $user_id = wp_create_user($username, $password, $email);

        // Add custom user meta
        add_user_meta($user_id, 'country_code', $country_code);
        add_user_meta($user_id, 'company_code', $company_code);
        add_user_meta($user_id, 'temp_customer_discount_id', $tmp_customer_id);
        add_user_meta($user_id, 'price_group', $price_group);

        echo 'Registration successful!';
    }
}

add_action('init', 'custom_user_registration');


function customer_test()
{
?>
    <div class="wrap">

        <h1>Test customer login</h1>
        <form id="login-form" action="" method="POST" style="width: 200px;">
            <input type="text" id="user-name" name="username" placeholder="Username or Email" style="margin-bottom:5px;" required>
            <input type="password" id="user-pass" name="password" placeholder="Password" style="margin-bottom:5px;" required>

            <input type="button" id="btn-test-login" name="login" value="Login">
        </form>
        <div id="user-content">
            <style>
                table {
                    width: 100%;
                    border-collapse: collapse;
                }

                th,
                td {
                    padding: 10px;
                    text-align: left;
                    border: 1px solid #ddd;
                }

                th {
                    background-color: #f2f2f2;
                }
            </style>
            <h3 id="country-code">Country code: </h3>
            <h3 id="company-code">Company code: </h3>
            <h3 id="customer-id">Customer Id: </h3>
            <h3 id="price-group">Price group: </h3>
            <h3 id="price-list">Price List: </h3>
            <h3 id="currency">Currency: </h3>
            <h3 id="allowed-codes">Allowed price codes: </h3>
            <h3 id="disallowed-codes">Allowed price codes: </h3>
            <h3>Products for Special/Discount (Contract Price)</h3>
            <div id="new-products"></div>
            <h3 id="map-products-count"></h3>
            <div id="map-products"></div>
        </div>
    </div>
<?php
}
