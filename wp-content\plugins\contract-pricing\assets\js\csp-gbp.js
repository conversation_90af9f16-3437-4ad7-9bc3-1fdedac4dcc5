jQuery(document).ready(function ($) {
    loadCustomers();
    loadProducts();
    // Load the data using DataTable
    var table = $('#pricingTableGbp').DataTable({
        ajax: {
            url: csp_gbp_ajax_object.ajaxurl,
            type: 'POST',
            data: { action: 'get_pricing_data_gbp' }
        }
    });

    // Open modal for adding new pricing
    $('#addNewPricingGbp').on('click', function () {
        $('#pricingModalGbp').show();
        $('#row_id_gbp').val('');
    });

    $('#closeModalGbp').on('click', function () {
        $('#pricingModalGbp').hide();
    });

    // Save pricing data
    $('#pricingFormGbp').on('submit', function (e) {
        e.preventDefault();
        var action = $('#row_id_gbp').val() ? 'edit_pricing_gbp' : 'save_pricing_gbp';
        $.post(csp_gbp_ajax_object.ajaxurl, $(this).serialize() + '&action=' + action, function (response) {
            if (response.success) {
                $('#pricingModalGbp').hide();
                table.ajax.reload();
            } else {
                alert(response.data.message);
            }
        });
    });

    // Load customers for dropdown
    function loadCustomers() {
        $.post(csp_gbp_ajax_object.ajaxurl, { action: 'get_customers' }, function (customers) {
            $('#customerGbp').html('');
            $.each(customers, function (i, customer) {
                $('#customerGbp').append('<option value="' + customer.id + '">' + customer.name + '</option>');
            });
        });
    }

    // Load products for dropdown
    function loadProducts() {
        $.post(csp_gbp_ajax_object.ajaxurl, { action: 'get_products' }, function (products) {
            $('#productGbp').html('');
            $.each(products, function (i, product) {
                $('#productGbp').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
        });
    }

    // Delete pricing data
    $('#pricingTableGbp').on('click', '.deletePricing', function () {
        if (confirm('Are you sure you want to delete this entry?')) {
            var id = $(this).data('id');
            $.post(csp_gbp_ajax_object.ajaxurl, { action: 'delete_pricing_gbp', id: id }, function () {
                table.ajax.reload();
            });
        }
    });

    // Edit pricing data)
    $('#pricingTableGbp').on('click', '.editPricing', function () {
        var id = $(this).data('id');
        $.post(csp_gbp_ajax_object.ajaxurl, { action: 'get_pricing_by_id_gbp', id: id }, function (response) {
            if (response.success) {
                var data = response.data;
                $('#row_id_gbp').val(data.id);
                loadCustomersById(data.customer_id);
                loadProductsById(data.product_sku);
                $('#new_price_gbp').val(data.new_price);
                $('#pricingModalGbp').show();
            } else {
                alert(response.data.message);
            }
        });
    });

    function loadCustomersById(selectedCustomerId) {
        $.post(csp_gbp_ajax_object.ajaxurl, { action: 'get_customers_gbp' }, function (customers) {
            $('#customerGbp').html('');  // Clear the dropdown
            $.each(customers, function (i, customer) {
                if(customer.id == selectedCustomerId) {
                    $('#customerGbp').append('<option value="' + customer.id + '" selected>' + customer.name + '</option>');
                } else {
                    $('#customerGbp').append('<option value="' + customer.id + '">' + customer.name + '</option>');
                }
            });
        });
    }

    function loadProductsById(selectedProductSku) {
        $.post(csp_gbp_ajax_object.ajaxurl, { action: 'get_products_gbp' }, function (products) {
            $('#productGbp').html('');  // Clear the dropdown
            $.each(products, function (i, product) {
                $('#productGbp').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
            if (selectedProductSku) {
                $('#productGbp').val(selectedProductSku);
            }
        });
    }
    $('.dataTables_length select').css({ 'width': '100px' });
    $("#closeModalGbp").on("click", function () {
        $('#pricingModal').hide();
    });
    $("#closeEur").on("click", function () {
        $('#pricingModalGbp').hide();
    });

});

