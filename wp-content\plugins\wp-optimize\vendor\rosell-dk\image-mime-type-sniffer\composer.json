{"name": "rosell-dk/image-mime-type-sniffer", "description": "Sniff mime type (images only)", "type": "library", "license": "MIT", "keywords": ["mime", "mime type", "image", "images"], "scripts": {"ci": ["@test", "@phpcs-all", "@composer validate --no-check-all --strict", "@phpstan"], "cs-fix-all": ["php-cs-fixer fix src"], "cs-fix": "php-cs-fixer fix", "cs-dry": "php-cs-fixer fix --dry-run --diff", "test": "phpunit --coverage-text=build/coverage.txt --coverage-clover=build/coverage.clover --coverage-html=build/coverage --whitelist=src tests", "test-no-cov": "phpunit --no-coverage tests", "test2": "phpunit tests", "phpcs": "phpcs --standard=phpcs-ruleset.xml", "phpcs-all": "phpcs --standard=phpcs-ruleset.xml src", "phpcbf": "phpcbf --standard=PSR2", "phpstan": "vendor/bin/phpstan analyse src --level=4"}, "extra": {"scripts-descriptions": {"ci": "Run tests before CI", "phpcs": "Checks coding styles (PSR2) of file/dir, which you must supply. To check all, supply 'src'", "phpcbf": "Fix coding styles (PSR2) of file/dir, which you must supply. To fix all, supply 'src'", "cs-fix-all": "Fix the coding style of all the source files, to comply with the PSR-2 coding standard", "cs-fix": "Fix the coding style of a PHP file or directory, which you must specify.", "test": "Launches the preconfigured PHPUnit"}}, "autoload": {"psr-4": {"ImageMimeTypeSniffer\\": "src/"}}, "autoload-dev": {"psr-4": {"ImageMimeTypeSniffer\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.bitwise-it.dk/contact", "role": "Project Author"}], "require": {"php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "3.*", "phpstan/phpstan": "^1.5", "mikey179/vfsstream": "^1.6"}, "config": {"sort-packages": true}}