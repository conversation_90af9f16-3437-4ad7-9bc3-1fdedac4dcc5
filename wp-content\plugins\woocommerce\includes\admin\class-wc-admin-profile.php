<?php
/**
 * Add extra profile fields for users in admin
 *
 * <AUTHOR>
 * @category Admin
 * @package  WooCommerce\Admin
 * @version  2.4.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

if ( ! class_exists( 'WC_Admin_Profile', false ) ) :

	/**
	 * WC_Admin_Profile Class.
	 */
	class WC_Admin_Profile {

		/**
		 * Hook in tabs.
		 */
		public function __construct() {
			add_action( 'show_user_profile', array( $this, 'add_customer_meta_fields' ) );
			add_action( 'edit_user_profile', array( $this, 'add_customer_meta_fields' ) );

			add_action( 'personal_options_update', array( $this, 'save_customer_meta_fields' ) );
			add_action( 'edit_user_profile_update', array( $this, 'save_customer_meta_fields' ) );

			// Enqueue Select2 for admin user pages
			add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_select2_scripts' ) );

			// Add JavaScript for billing address auto-population
			add_action( 'admin_footer-user-edit.php', array( $this, 'add_billing_address_script' ) );
			add_action( 'admin_footer-profile.php', array( $this, 'add_billing_address_script' ) );

			// Add Ship To addresses display
			add_action( 'show_user_profile', array( $this, 'display_ship_to_addresses' ) );
			add_action( 'edit_user_profile', array( $this, 'display_ship_to_addresses' ) );
		}

		/**
		 * Get Address Fields for the edit user pages.
		 *
		 * @return array Fields to display which are filtered through woocommerce_customer_meta_fields before being returned
		 */
		public function get_customer_meta_fields() {
			$show_fields = apply_filters(
				'woocommerce_customer_meta_fields',
				array(
					'billing'  => array(
						'title'  => __( 'Customer billing address', 'woocommerce' ),
						'fields' => array(
							'billing_first_name' => array(
								'label'       => __( 'First name', 'woocommerce' ),
								'description' => '',
								'custom_attributes' => array( 'readonly' => 'readonly' ),
							),
							'billing_last_name'  => array(
								'label'       => __( 'Last name', 'woocommerce' ),
								'description' => '',
								'custom_attributes' => array( 'readonly' => 'readonly' ),
							),
							'billing_company'    => array(
								'label'       => __( 'Company', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'custom_attributes' => array( 'readonly' => 'readonly' ),
								'class'       => 'sap-auto-populated',
							),
							'billing_address_1'  => array(
								'label'       => __( 'Address line 1', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'custom_attributes' => array( 'readonly' => 'readonly' ),
								'class'       => 'sap-auto-populated',
							),
							'billing_address_2'  => array(
								'label'       => __( 'Address line 2', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'custom_attributes' => array( 'readonly' => 'readonly' ),
								'class'       => 'sap-auto-populated',
							),
							'billing_city'       => array(
								'label'       => __( 'City', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'custom_attributes' => array( 'readonly' => 'readonly' ),
								'class'       => 'sap-auto-populated',
							),
							'billing_postcode'   => array(
								'label'       => __( 'Postcode / ZIP', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'custom_attributes' => array( 'readonly' => 'readonly' ),
								'class'       => 'sap-auto-populated',
							),
							'billing_country'    => array(
								'label'       => __( 'Country / Region', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'class'       => 'js_field-country sap-auto-populated',
								'type'        => 'select',
								'options'     => array( '' => __( 'Select a country / region&hellip;', 'woocommerce' ) ) + WC()->countries->get_allowed_countries(),
								'custom_attributes' => array( 'disabled' => 'disabled' ),
							),
							'billing_state'      => array(
								'label'       => __( 'State / County', 'woocommerce' ),
								'description' => __( 'Auto-populated from customer database', 'woocommerce' ),
								'class'       => 'js_field-state sap-auto-populated',
								'custom_attributes' => array( 'readonly' => 'readonly' ),
							),
							'billing_phone'      => array(
								'label'       => __( 'Phone', 'woocommerce' ),
								'description' => '',
								'custom_attributes' => array( 'readonly' => 'readonly' ),
							),
							/*'billing_email'      => array(
								'label'       => __( 'Email address', 'woocommerce' ),
								'description' => '',
							),*/
						),
					),
					/*'shipping' => array(
						'title'  => __( 'Customer shipping address', 'woocommerce' ),
						'fields' => array(
							'copy_billing'        => array(
								'label'       => __( 'Copy from billing address', 'woocommerce' ),
								'description' => '',
								'class'       => 'js_copy-billing',
								'type'        => 'button',
								'text'        => __( 'Copy', 'woocommerce' ),
							),
							'shipping_first_name' => array(
								'label'       => __( 'First name', 'woocommerce' ),
								'description' => '',
							),
							'shipping_last_name'  => array(
								'label'       => __( 'Last name', 'woocommerce' ),
								'description' => '',
							),
							'shipping_company'    => array(
								'label'       => __( 'Company', 'woocommerce' ),
								'description' => '',
							),
							'shipping_address_1'  => array(
								'label'       => __( 'Address line 1', 'woocommerce' ),
								'description' => '',
							),
							'shipping_address_2'  => array(
								'label'       => __( 'Address line 2', 'woocommerce' ),
								'description' => '',
							),
							'shipping_city'       => array(
								'label'       => __( 'City', 'woocommerce' ),
								'description' => '',
							),
							'shipping_postcode'   => array(
								'label'       => __( 'Postcode / ZIP', 'woocommerce' ),
								'description' => '',
							),
							'shipping_country'    => array(
								'label'       => __( 'Country / Region', 'woocommerce' ),
								'description' => '',
								'class'       => 'js_field-country',
								'type'        => 'select',
								'options'     => array( '' => __( 'Select a country / region&hellip;', 'woocommerce' ) ) + WC()->countries->get_allowed_countries(),
							),
							'shipping_state'      => array(
								'label'       => __( 'State / County', 'woocommerce' ),
								'description' => __( 'State / County or state code', 'woocommerce' ),
								'class'       => 'js_field-state',
							),
							'shipping_phone'      => array(
								'label'       => __( 'Phone', 'woocommerce' ),
								'description' => '',
							),
						),
					),*/
				)
			);
			return $show_fields;
		}

		/**
		 * Enqueue Select2 scripts for admin user pages
		 */
		public function enqueue_select2_scripts( $hook ) {
			// Only load on user edit pages
			if ( $hook !== 'user-edit.php' && $hook !== 'profile.php' ) {
				return;
			}

			// Check if WooCommerce Select2 is available
			if ( function_exists( 'wc_enqueue_js' ) ) {
				// Use WooCommerce's Select2
				wp_enqueue_script( 'selectWoo' );
				wp_enqueue_style( 'select2' );
			} else {
				// Fallback to WordPress core Select2 if available
				wp_enqueue_script( 'select2' );
				wp_enqueue_style( 'select2' );
			}
		}

		/**
		 * Show Address Fields on edit user pages.
		 *
		 * @param WP_User $user
		 */
		public function add_customer_meta_fields( $user ) {
			if ( ! apply_filters( 'woocommerce_current_user_can_edit_customer_meta_fields', current_user_can( 'manage_woocommerce' ), $user->ID ) ) {
				return;
			}

			$show_fields = $this->get_customer_meta_fields();

			foreach ( $show_fields as $fieldset_key => $fieldset ) :
				?>
				<h2><?php echo $fieldset['title']; ?></h2>
				<table class="form-table" id="<?php echo esc_attr( 'fieldset-' . $fieldset_key ); ?>">
					<?php foreach ( $fieldset['fields'] as $key => $field ) : ?>
						<tr>
							<th>
								<label for="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $field['label'] ); ?></label>
							</th>
							<td>
								<?php if ( ! empty( $field['type'] ) && 'select' === $field['type'] ) : ?>
									<?php
									// Build custom attributes string for select
									$select_custom_attributes = '';
									if ( ! empty( $field['custom_attributes'] ) && is_array( $field['custom_attributes'] ) ) {
										foreach ( $field['custom_attributes'] as $attribute => $attribute_value ) {
											$select_custom_attributes .= esc_attr( $attribute ) . '="' . esc_attr( $attribute_value ) . '" ';
										}
									}
									?>
									<select name="<?php echo esc_attr( $key ); ?>" id="<?php echo esc_attr( $key ); ?>" class="<?php echo isset( $field['class'] ) ? esc_attr( $field['class'] ) : ''; ?>" style="width: 25em;" <?php echo $select_custom_attributes; ?>>
										<?php
											$selected = esc_attr( get_user_meta( $user->ID, $key, true ) );
										foreach ( $field['options'] as $option_key => $option_value ) :
											?>
											<option value="<?php echo esc_attr( $option_key ); ?>" <?php selected( $selected, $option_key, true ); ?>><?php echo esc_html( $option_value ); ?></option>
										<?php endforeach; ?>
									</select>
								<?php elseif ( ! empty( $field['type'] ) && 'checkbox' === $field['type'] ) : ?>
									<input type="checkbox" name="<?php echo esc_attr( $key ); ?>" id="<?php echo esc_attr( $key ); ?>" value="1" class="<?php echo esc_attr( $field['class'] ); ?>" <?php checked( (int) get_user_meta( $user->ID, $key, true ), 1, true ); ?> />
								<?php elseif ( ! empty( $field['type'] ) && 'button' === $field['type'] ) : ?>
									<button type="button" id="<?php echo esc_attr( $key ); ?>" class="button <?php echo esc_attr( $field['class'] ); ?>"><?php echo esc_html( $field['text'] ); ?></button>
								<?php else : ?>
									<?php
									// Build custom attributes string
									$custom_attributes = '';
									if ( ! empty( $field['custom_attributes'] ) && is_array( $field['custom_attributes'] ) ) {
										foreach ( $field['custom_attributes'] as $attribute => $attribute_value ) {
											$custom_attributes .= esc_attr( $attribute ) . '="' . esc_attr( $attribute_value ) . '" ';
										}
									}
									?>
									<input type="text" name="<?php echo esc_attr( $key ); ?>" id="<?php echo esc_attr( $key ); ?>" value="<?php echo esc_attr( $this->get_user_meta( $user->ID, $key ) ); ?>" class="<?php echo ( ! empty( $field['class'] ) ? esc_attr( $field['class'] ) : 'regular-text' ); ?>" <?php echo $custom_attributes; ?> />
								<?php endif; ?>
								<?php if ( ! empty( $field['description'] ) ) : ?>
									<p class="description"><?php echo wp_kses_post( $field['description'] ); ?></p>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
				</table>
				<?php
			endforeach;
		}

		/**
		 * Save Address Fields on edit user pages.
		 *
		 * @param int $user_id User ID of the user being saved
		 */
		public function save_customer_meta_fields( $user_id ) {
			if ( ! apply_filters( 'woocommerce_current_user_can_edit_customer_meta_fields', current_user_can( 'manage_woocommerce' ), $user_id ) ) {
				return;
			}

			$save_fields = $this->get_customer_meta_fields();

			foreach ( $save_fields as $fieldset_type => $fieldset ) {

				foreach ( $fieldset['fields'] as $key => $field ) {

					if ( isset( $field['type'] ) && 'checkbox' === $field['type'] ) {
						update_user_meta( $user_id, $key, isset( $_POST[ $key ] ) );
					} elseif ( isset( $_POST[ $key ] ) ) {
						update_user_meta( $user_id, $key, wc_clean( $_POST[ $key ] ) );
					}
				}

				// Skip firing the action for any non-internal fieldset types.
				if ( ! in_array( $fieldset_type, array( 'billing', 'shipping' ), true ) ) {
					continue;
				}

				// Fieldset type is an internal address type.
				$address_type = $fieldset_type;

				/**
				 * Hook: woocommerce_customer_save_address.
				 *
				 * Fires after a customer address has been saved on the user profile admin screen.
				 *
				 * @since 8.5.0
				 * @param int    $user_id User ID being saved.
				 * @param string $address_type Type of address; 'billing' or 'shipping'.
				 */
				do_action( 'woocommerce_customer_save_address', $user_id, $address_type );
			}
		}

		/**
		 * Get user meta for a given key, with fallbacks to core user info for pre-existing fields.
		 *
		 * @since 3.1.0
		 * @param int    $user_id User ID of the user being edited
		 * @param string $key     Key for user meta field
		 * @return string
		 */
		protected function get_user_meta( $user_id, $key ) {
			$value           = get_user_meta( $user_id, $key, true );
			$existing_fields = array( 'billing_first_name', 'billing_last_name' );
			if ( ! $value && in_array( $key, $existing_fields ) ) {
				$value = get_user_meta( $user_id, str_replace( 'billing_', '', $key ), true );
			} elseif ( ! $value && ( 'billing_email' === $key ) ) {
				$user  = get_userdata( $user_id );
				$value = $user->user_email;
			}

			return $value;
		}

		/**
		 * Add JavaScript for billing address auto-population from SAP customer data
		 */
		public function add_billing_address_script() {
			?>
			<style type="text/css">
				/* Styling for SAP auto-populated fields */
				.sap-auto-populated {
					background-color: #f9f9f9 !important;
					cursor: not-allowed !important;
				}
				.sap-auto-populated:focus {
					box-shadow: 0 0 0 1px #ccc !important;
				}
				.sap-auto-populated option {
					background-color: #f9f9f9 !important;
				}

				/* Select2 styling for customer ID dropdown */
				.select2-container--default .select2-selection--single {
					height: 30px;
					line-height: 28px;
				}
				.select2-container--default .select2-selection--single .select2-selection__rendered {
					padding-left: 8px;
					padding-right: 20px;
				}
				.select2-container--default .select2-selection--single .select2-selection__arrow {
					height: 28px;
				}
				.select2-container {
					width: 25em !important;
				}
			</style>
			<script type="text/javascript">
			jQuery(document).ready(function($) {
				console.log('🏠 WooCommerce Billing Address Auto-Population loaded');

				// Initialize Select2 for customer ID dropdown
				initializeCustomerIdSelect2();

				// Add visual indicators and prevent manual editing
				initializeBillingFieldRestrictions();

				// Check if customer ID dropdown exists (from customer-price-group plugin)
				if ($('#customer_id').length > 0) {
					console.log('✅ Customer ID dropdown found - setting up billing address auto-population');

					// Listen for customer ID changes
					$(document).on('change', '#customer_id', function() {
						var customerId = $(this).val();
						console.log('🔄 Customer ID changed in WooCommerce context:', customerId);

						if (customerId && customerId.length > 0) {
							populateBillingAddressFromSAP(customerId);
						} else {
							clearBillingAddressFields();
						}
					});

					// Also listen for custom events from the customer-price-group plugin
					$(document).on('customer_data_loaded', function(event, data) {
						console.log('📡 Received customer data event:', data);
						if (data && data.billing_company !== undefined) {
							populateWooCommerceBillingFields(data);
						}
					});
				} else {
					console.log('ℹ️ Customer ID dropdown not found - billing address auto-population not available');
				}

				// Function to initialize Select2 for customer ID dropdown
				function initializeCustomerIdSelect2() {
					console.log('🔍 Initializing Select2 for customer ID dropdown...');

					// Check if customer ID dropdown exists
					if ($('#customer_id').length > 0) {
						// Try WooCommerce's selectWoo first, then fallback to select2
						var select2Method = null;
						if (typeof $.fn.selectWoo !== 'undefined') {
							select2Method = 'selectWoo';
							console.log('✅ SelectWoo (WooCommerce) available, initializing customer ID dropdown');
						} else if (typeof $.fn.select2 !== 'undefined') {
							select2Method = 'select2';
							console.log('✅ Select2 available, initializing customer ID dropdown');
						}

						if (select2Method) {
							// Initialize Select2/SelectWoo with search functionality
							$('#customer_id')[select2Method]({
								placeholder: 'Select a Customer ID',
								allowClear: true,
								width: '25em',
								matcher: function(params, data) {
									// If there are no search terms, return all data
									if ($.trim(params.term) === '') {
										return data;
									}

									// Skip if there is no 'text' property
									if (typeof data.text === 'undefined') {
										return null;
									}

									// Search in both the customer ID and the additional info (company code, country)
									var searchText = data.text.toLowerCase();
									var searchTerm = params.term.toLowerCase();

									// Check if the search term matches the customer ID or additional info
									if (searchText.indexOf(searchTerm) > -1) {
										return data;
									}

									// Return null if the term should not be displayed
									return null;
								}
							});

							console.log('🎉 ' + select2Method + ' initialized successfully for customer ID dropdown');
						} else {
							console.log('⚠️ Neither SelectWoo nor Select2 available - falling back to regular dropdown');
						}
					} else {
						console.log('ℹ️ Customer ID dropdown not found - Select2 not initialized');
					}
				}

				// Function to fetch and populate billing address from SAP database
				function populateBillingAddressFromSAP(customerId) {
					console.log('🚀 Fetching billing address for customer:', customerId);

					$.ajax({
						url: ajaxurl,
						type: 'POST',
						data: {
							action: 'fetch_company_country_codes',
							customer_id: customerId
						},
						success: function(response) {
							console.log('📥 SAP billing address response:', response);
							if (response.success && response.data) {
								populateWooCommerceBillingFields(response.data);
							} else {
								console.log('⚠️ No billing address data available for customer:', customerId);
							}
						},
						error: function(xhr, status, error) {
							console.error('❌ Error fetching billing address:', error);
						}
					});
				}

				// Function to initialize billing field restrictions
				function initializeBillingFieldRestrictions() {
					console.log('🔒 Initializing billing field restrictions...');

					// List of SAP auto-populated billing fields
					const sapBillingFields = [
						'billing_company', 'billing_address_1', 'billing_address_2',
						'billing_city', 'billing_postcode', 'billing_country', 'billing_state'
					];

					// Add event handlers to prevent manual editing
					sapBillingFields.forEach(function(fieldId) {
						const $field = $('#' + fieldId);
						if ($field.length > 0) {
							// Prevent keyboard input
							$field.on('keydown keypress keyup paste', function(e) {
								if (!$(this).data('sap-updating')) {
									e.preventDefault();
									showBillingAddressNotice('This field is auto-populated from the customer database. Please select a Customer ID to update it.', 'info');
									return false;
								}
							});

							// Prevent mouse selection for dropdowns
							if ($field.is('select')) {
								$field.on('mousedown', function(e) {
									if (!$(this).data('sap-updating')) {
										e.preventDefault();
										showBillingAddressNotice('This field is auto-populated from the customer database. Please select a Customer ID to update it.', 'info');
										return false;
									}
								});
							}

							console.log('🔒 Restricted field:', fieldId);
						}
					});
				}

				// Function to populate WooCommerce billing address fields
				function populateWooCommerceBillingFields(data) {
					console.log('🏠 Populating WooCommerce billing address fields...');

					// Field mappings from SAP database to WooCommerce fields
					const fieldMappings = {
						'billing_company': data.billing_company || '',
						'billing_address_1': data.billing_address_1 || '',
						'billing_address_2': data.billing_address_2 || '',
						'billing_city': data.billing_city || '',
						'billing_postcode': data.billing_postcode || '',
						'billing_country': data.billing_country || '',
						'billing_state': data.billing_state || ''
					};

					// Populate each field
					let fieldsPopulated = 0;
					$.each(fieldMappings, function(fieldId, value) {
						const $field = $('#' + fieldId);
						if ($field.length > 0 && value) {
							// Temporarily allow updates
							$field.data('sap-updating', true);

							// Remove disabled attribute temporarily for population
							const wasDisabled = $field.prop('disabled');
							const wasReadonly = $field.prop('readonly');

							$field.prop('disabled', false).prop('readonly', false);
							$field.val(value);

							// Restore original state
							if (wasDisabled) $field.prop('disabled', true);
							if (wasReadonly) $field.prop('readonly', true);

							console.log('✅ Populated', fieldId + ':', value);
							fieldsPopulated++;

							// Trigger change event for country/state dropdowns
							if (fieldId === 'billing_country' || fieldId === 'billing_state') {
								$field.trigger('change');
							}

							// Remove update flag after a short delay
							setTimeout(function() {
								$field.data('sap-updating', false);
							}, 100);
						} else if ($field.length === 0) {
							console.log('⚠️ Field not found:', fieldId);
						}
					});

					if (fieldsPopulated > 0) {
						console.log('🎉 Successfully populated', fieldsPopulated, 'billing address fields');

						// Show success message
						showBillingAddressNotice('Billing address auto-populated from customer database!', 'success');
					} else {
						console.log('ℹ️ No billing address fields were populated');
					}
				}

				// Function to clear billing address fields
				function clearBillingAddressFields() {
					console.log('🧹 Clearing billing address fields');
					const billingFields = ['billing_company', 'billing_address_1', 'billing_address_2', 'billing_city', 'billing_postcode', 'billing_country', 'billing_state'];

					billingFields.forEach(function(fieldId) {
						const $field = $('#' + fieldId);
						if ($field.length > 0) {
							$field.val('');
						}
					});
				}

				// Function to show notices
				function showBillingAddressNotice(message, type) {
					// Remove existing notices
					$('.billing-address-notice').remove();

					// Create notice
					const noticeClass = 'notice notice-' + type + ' billing-address-notice is-dismissible';
					const notice = $('<div class="' + noticeClass + '"><p>' + message + '</p></div>');

					// Add dismiss button
					notice.append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');

					// Insert notice
					if ($('#fieldset-billing').length > 0) {
						$('#fieldset-billing').before(notice);
					} else if ($('.wrap h1').length > 0) {
						$('.wrap h1').after(notice);
					}

					// Handle dismiss
					notice.find('.notice-dismiss').on('click', function() {
						notice.fadeOut(300, function() { $(this).remove(); });
					});

					// Auto-hide success notices
					if (type === 'success') {
						setTimeout(function() {
							notice.fadeOut(300, function() { $(this).remove(); });
						}, 5000);
					}
				}
			});
			</script>
			<?php
		}

		/**
		 * Display Ship To addresses from WCMCA plugin metadata
		 */
		public function display_ship_to_addresses( $user ) {
			if ( ! apply_filters( 'woocommerce_current_user_can_edit_customer_meta_fields', current_user_can( 'manage_woocommerce' ), $user->ID ) ) {
				return;
			}

			// Get addresses from _wcmca_additional_addresses metadata
			$addresses = get_user_meta( $user->ID, '_wcmca_additional_addresses', true );

			if ( ! is_array( $addresses ) ) {
				$addresses = array();
			}

			// Filter for shipping addresses only
			$shipping_addresses = array();
			foreach ( $addresses as $address_id => $address ) {
				if ( isset( $address['type'] ) && $address['type'] === 'shipping' ) {
					$shipping_addresses[$address_id] = $address;
				}
			}

			?>
			<h2><?php esc_html_e( 'Ship To Addresses', 'woocommerce' ); ?></h2>
			<table class="form-table" id="ship-to-addresses-table">
				<tbody>
					<?php if ( empty( $shipping_addresses ) ): ?>
						<tr>
							<td colspan="2">
								<p class="description"><?php esc_html_e( 'No Ship To addresses found for this user.', 'woocommerce' ); ?></p>
							</td>
						</tr>
					<?php else: ?>
						<?php foreach ( $shipping_addresses as $address_id => $address ): ?>
							<tr class="ship-to-address-row" data-address-id="<?php echo esc_attr( $address_id ); ?>">
								<th scope="row">
									<strong><?php echo esc_html( $address['address_internal_name'] ?? 'Unnamed Address' ); ?></strong>
									<?php if ( isset( $address['shipping_is_default_address'] ) && $address['shipping_is_default_address'] ): ?>
										<span class="ship-to-default-badge">
											<?php esc_html_e( 'Default', 'woocommerce' ); ?>
										</span>
									<?php endif; ?>

									<!-- Display Address ID -->
									<?php
									$display_address_id = isset( $address['address_id'] ) ? $address['address_id'] : $address_id;
									?>
									<div class="ship-to-address-id">
										<strong>Address ID:</strong> <code><?php echo esc_html( $display_address_id ); ?></code>
									</div>
								</th>
								<td>
									<div class="ship-to-address-details">
										<?php
										// Display address fields
										$address_fields = array(
											'shipping_company' => __( 'Company', 'woocommerce' ),
											'shipping_first_name' => __( 'First Name', 'woocommerce' ),
											'shipping_last_name' => __( 'Last Name', 'woocommerce' ),
											'shipping_address_1' => __( 'Address Line 1', 'woocommerce' ),
											'shipping_address_2' => __( 'Address Line 2', 'woocommerce' ),
											'shipping_city' => __( 'City', 'woocommerce' ),
											'shipping_state' => __( 'State/Province', 'woocommerce' ),
											'shipping_postcode' => __( 'Postal Code', 'woocommerce' ),
											'shipping_country' => __( 'Country', 'woocommerce' ),
											'shipping_phone' => __( 'Phone', 'woocommerce' ),
										);

										foreach ( $address_fields as $field_key => $field_label ):
											if ( isset( $address[$field_key] ) && !empty( $address[$field_key] ) ):
												$field_value = $address[$field_key];

												// Convert country code to country name
												if ( $field_key === 'shipping_country' ) {
													$countries = WC()->countries->get_countries();
													$field_value = isset( $countries[$field_value] ) ? $countries[$field_value] : $field_value;
												}

												// Convert state code to state name
												if ( $field_key === 'shipping_state' && isset( $address['shipping_country'] ) ) {
													$states = WC()->countries->get_states( $address['shipping_country'] );
													if ( is_array( $states ) && isset( $states[$field_value] ) ) {
														$field_value = $states[$field_value];
													}
												}
										?>
											<div class="ship-to-address-field">
												<strong><?php echo esc_html( $field_label ); ?>:</strong>
												<span><?php echo esc_html( $field_value ); ?></span>
											</div>
										<?php
											endif;
										endforeach;
										?>
									</div>
								</td>
							</tr>
						<?php endforeach; ?>
					<?php endif; ?>
				</tbody>
			</table>

			<style>
			#ship-to-addresses-table .ship-to-address-row {
				border-top: 1px solid #ddd;
			}
			#ship-to-addresses-table .ship-to-address-row:first-child {
				border-top: none;
			}
			#ship-to-addresses-table th {
				width: 200px;
				vertical-align: top;
				padding-top: 15px;
			}
			#ship-to-addresses-table td {
				padding-top: 10px;
			}
			.ship-to-address-details {
				background: #f9f9f9;
				padding: 12px;
				border-radius: 4px;
				margin-bottom: 10px;
			}
			.ship-to-address-field {
				font-size: 13px;
				margin-bottom: 6px;
			}
			.ship-to-address-field:last-child {
				margin-bottom: 0;
			}
			.ship-to-address-field strong {
				display: inline-block;
				min-width: 120px;
				color: #333;
			}
			.ship-to-default-badge {
				background: #0073aa;
				color: white;
				padding: 2px 6px;
				border-radius: 3px;
				font-size: 11px;
				margin-left: 5px;
				font-weight: normal !important;
			}
			.ship-to-address-id {
				margin: 4px 0;
				font-size: 11px;
				color: #666;
				font-family: monospace;
			}
			.ship-to-address-id code {
				background: #f0f8ff;
				padding: 2px 4px;
				border-radius: 2px;
				color: #0073aa;
				font-weight: bold;
			}
			</style>
			<?php
		}
	}

endif;

return new WC_Admin_Profile();
