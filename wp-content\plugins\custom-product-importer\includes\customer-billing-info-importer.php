<?php
function import_billing_info($batch) {
    $updated_count = 0;

    foreach ($batch as $row) {
        $customer_number = sanitize_text_field($row['customer_number']);
        if (empty($customer_number)) {
            continue;
        }

        // Get user ID based on _customer meta field
        global $wpdb;
        $user_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta} WHERE meta_key = '_customer' AND meta_value = %s LIMIT 1",
            $customer_number
        ));

        if ($user_id) {
            // Sanitize and update meta fields
            update_user_meta($user_id, 'billing_first_name', sanitize_text_field($row['customer_name']));
            update_user_meta($user_id, 'billing_company', sanitize_text_field($row['customer_name']));
            update_user_meta($user_id, 'billing_address_1', sanitize_text_field($row['street']));
            update_user_meta($user_id, 'billing_city', sanitize_text_field($row['city']));
            update_user_meta($user_id, 'billing_state', sanitize_text_field($row['sap_region']));
            update_user_meta($user_id, 'billing_postcode', sanitize_text_field($row['postal_code']));
            update_user_meta($user_id, 'billing_country', sanitize_text_field($row['country']));
            update_user_meta($user_id, '_pricegroup', sanitize_text_field($row['price_group']));

            $updated_count++;
        }
    }

    wp_send_json_success("Processed $updated_count rows.");
}
