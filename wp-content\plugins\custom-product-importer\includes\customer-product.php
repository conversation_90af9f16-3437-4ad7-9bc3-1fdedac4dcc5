<?php

function get_product_for_customer($username, $userpass)
{
    // global $wpdb;
    // $user = get_user_by('login', $username);
    // if (!$user) {
    //     return false; // User not found
    // }
    // $total_data = [];
    // $customer_id = "";
    // $price_group = "";
    // $currency = "";

    // $user_id = checkPassword($userpass, $user);
    // if ($user_id) {
    //     $total_data['country_code'] = get_user_meta($user_id, 'country_code', true);
    //     $total_data['company_code'] = get_user_meta($user_id, 'company_code', true);
    //     $customer_id = get_user_meta($user_id, 'temp_customer_discount_id', true);
    //     $customer_id = (int)$customer_id;
    //     $total_data['customer_id'] = $customer_id;
    //     $price_group = get_user_meta($user_id, 'price_group', true);
    //     $total_data['price_group'] = $price_group;
    // }

    // if ($total_data['company_code'] == "3090") {
    //     $total_data['price_list'] = "G1-USD";
    //     $currency = "USD";
    //     $total_data['currency'] = $currency;
    // } else {
    //     if ($total_data['country_code'] == "GB") {
    //         $total_data['price_list'] = "G3-GBP";
    //         $currency = "GBP";
    //         $total_data['currency'] = $currency;
    //     } else {
    //         $total_data['price_list'] = "G3-EUR";
    //         $currency = "EUR";
    //         $total_data['currency'] = $currency;
    //     }
    // }

    // // Get contract price data.
    // $table_contract_pricing = $wpdb->prefix . "contract_pricing";
    // $new_price_products = [];
    // $sql = "SELECT * FROM $table_contract_pricing WHERE customer_id=$customer_id";
    // $check_result = $wpdb->get_results($sql);
    // if (!empty($check_result)) {
    //     foreach ($check_result as $new_price_product) {
    //         $product = get_product_by_sku($new_price_product->product_sku, true);
    //         if($product){
    //             $product['description'] = $new_price_product->description;
    //             $product['sku'] = $new_price_product->product_sku;
    //             $product['price'] = "* " . $new_price_product->new_price . " " .$currency;
    //             $product['stock'] = "100";
    //             $new_price_products[] = $product;
    //         }
    //         else{
    //             return "New products are failed.";
    //         }
    //     }
    // }
    // $total_data['new_price_products'] = $new_price_products;

    // // Getting visible post codes based on customer price_group.
    // $allowed_price_codes = [];
    // $allowed_price_code_ids = [];
    // $disallowed_price_codes = [];
    // $code_group_relation_table = $wpdb->prefix . 'price_code_group_relation';
    // $price_code_list_table = $wpdb->prefix.'price_code_list';
    // $sql = "SELECT pcl.price_code_title, pcgr.status, pcl.id
    //         FROM $code_group_relation_table pcgr
    //         JOIN $price_code_list_table pcl
    //         ON pcgr.price_code_id=pcl.id
    //         WHERE pcgr.price_group_id=$price_group";
    // $check_result = $wpdb->get_results($sql);
    // if(!empty($check_result)){
    //     foreach($check_result as $price_code_status){
    //         if($price_code_status->status == "Yes"){
    //             $allowed_price_codes[] = $price_code_status->price_code_title;
    //             $allowed_price_code_ids[] = $price_code_status->id;
    //         }else{
    //             $disallowed_price_codes[] = $price_code_status->price_code_title;
    //         }
    //     }
    // }    
    // $total_data['allowed_price_codes'] = $allowed_price_codes;
    // $total_data['disallowed_price_codes'] = $disallowed_price_codes;

    // $allowed_products = [];
    // $price_list_table = $wpdb->prefix . 'price_list';
    // if(count($allowed_price_code_ids) > 0){
    //     foreach($allowed_price_code_ids as $id_index => $allowed_price_code_id){
    //         $sql = "SELECT product_sku, description, price FROM $price_list_table WHERE price_code_id=$allowed_price_code_id";
    //         $result = $wpdb->get_results($sql);
    //         if(!empty($result)){
    //             foreach($result as $price_product){
    //                 $tmp_allowed_product = get_product_by_sku($price_product->product_sku);
    //                 if($tmp_allowed_product) {
    //                     $tmp_allowed_product['description'] = $price_product->description;
    //                     $tmp_allowed_product['sku'] = $price_product->product_sku;
    //                     $tmp_allowed_product['price'] = $price_product->price . " " .$currency;
    //                     $tmp_allowed_product['stock'] = "100";
    //                     $tmp_allowed_product['price_code'] = $allowed_price_codes[$id_index];
    //                     $allowed_products[] = $tmp_allowed_product;
    //                 }
    //             }
    //         }
    //     }
    // }
    // $total_data['allowed_products'] = $allowed_products;
    // return $total_data;
    return $total_data="1";
}

function checkPassword($pss, $usr)
{
    if (wp_check_password($pss, $usr->user_pass, $usr->ID)) {
        return $usr->ID;
    } else {
        return false;
    }
}

function get_product_by_sku($sku, $new = false)
{
    global $wpdb;

    $price_list_table = $wpdb->prefix . 'price_list';
    $contract_pricing_table = $wpdb->prefix . 'contract_pricing';
    $price_code_list_table = $wpdb->prefix . 'price_code_list';
    // Get the product ID by SKU
    $product_id = wc_get_product_id_by_sku($sku);
    // Check if the product exists
    if ($product_id) {
        $tmp_prod_array = [];
        // Get the product object
        $product = wc_get_product($product_id);
        $tmp_prod_array['name'] = $product->get_name();
        $thumbnail = get_the_post_thumbnail_url($product_id, 'full');
        $thumbnail == null ? $tmp_prod_array['thumbnail'] = "No image" : $tmp_prod_array['thumbnail'] = $thumbnail;
        $tmp_prod_array['ce_approved'] = "Yes";

        $sku = $sku;
        if ($new) {
            $sql = "SELECT pcl.price_code_title
                    FROM $price_list_table pl
                    JOIN $price_code_list_table pcl
                    ON pl.price_code_id = pcl.id
                    WHERE pl.product_sku = '$sku'";
            $check_result = $wpdb->get_results($sql);
            $price_code = $check_result[0]->price_code_title;
            $price_code == null ? $tmp_prod_array['price_code']="Not in List Price Items" : $tmp_prod_array['price_code']=$price_code;
        }
        $tmp_prod_array['brand'] = get_post_meta( $product_id, '_brand', true );
        $tmp_prod_array['product_line'] = get_post_meta( $product_id, '_product_line', true );
        $tmp_prod_array['product_family'] = get_post_meta( $product_id, '_product_family', true );
        $tmp_prod_array['product_series'] = get_post_meta( $product_id, '_product_series', true );
        $tmp_prod_array['model_size'] = get_post_meta( $product_id, '_model_size', true );

        return $tmp_prod_array;
    } else {
        return false;
    }
}
