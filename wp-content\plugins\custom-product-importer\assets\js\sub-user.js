jQuery(document).ready(function ($) {
    const uploadPanel = $('#subuser-upload-pan');
    const progressText = $('#subuser-progress-text');
    const progressBar = $('#subuser-progress-bar');

    $('#btn-subuser-excel-file').on('click', function () {
        const fileInput = document.getElementById('subuser-excel-file');
        const file = fileInput.files[0];

        if (!file) {
            alert('Please select a file before clicking Import.');
            return;
        }

        uploadPanel.show();

        const reader = new FileReader();

        reader.onload = function (e) {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const sheetData = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);

            const batchSize = 10;
            const totalBatches = Math.ceil(sheetData.length / batchSize);
            let currentBatch = 0;
            let currentBatchProgress = 0;
            const processBatch = () => {
                if (currentBatch >= totalBatches) {
                    uploadPanel.hide();
                    progressText.text('Upload complete.');
                    progressBar.css('width', '100%');
                    return;
                }

                const batchData = sheetData.slice(currentBatch * batchSize, (currentBatch + 1) * batchSize);

                $.ajax({
                    url: ajaxurl,
                    method: 'POST',
                    data: {
                        action: 'process_sub_users',
                        data: batchData,
                    },
                    success: function (response) {
                        if (response.success) {
                            currentBatch++;
                            currentBatchProgress += batchData.length;
                            const progressPercent = Math.round((currentBatchProgress / totalBatches) * 100);
                            progressText.text(`${progressPercent}%`);
                            progressBar.css('width', `${progressPercent}%`);
                            processBatch();
                        } else {
                            console.log(response);
                            alert(`Error processing batch ${currentBatch + 1}`);
                            uploadPanel.hide();
                        }
                    },
                    error: function () {
                        alert('Error communicating with the server.');
                        uploadPanel.hide();
                    },
                });
            };

            processBatch();
        };

        reader.readAsArrayBuffer(file);
    });
});
