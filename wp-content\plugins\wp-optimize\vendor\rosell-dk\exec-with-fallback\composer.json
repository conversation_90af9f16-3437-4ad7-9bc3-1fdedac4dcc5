{"name": "rosell-dk/exec-with-fallback", "description": "An exec() with fallback to emulations (proc_open, etc)", "type": "library", "license": "MIT", "keywords": ["exec", "open_proc", "command", "fallback", "sturdy", "resiliant"], "scripts": {"ci": ["@test", "@phpcs-all", "@composer validate --no-check-all --strict", "@phpstan-global"], "test": "./vendor/bin/phpunit --coverage-text", "test-41": "phpunit --coverage-text --configuration 'phpunit-41.xml.dist'", "phpunit": "phpunit --coverage-text", "test-no-cov": "phpunit --no-coverage", "cs-fix-all": ["php-cs-fixer fix src"], "cs-fix": "php-cs-fixer fix", "cs-dry": "php-cs-fixer fix --dry-run --diff", "phpcs": "phpcs --standard=PSR2", "phpcs-all": "phpcs --standard=PSR2 src", "phpcbf": "phpcbf --standard=PSR2", "phpstan": "vendor/bin/phpstan analyse src --level=4", "phpstan-global-old": "~/.composer/vendor/bin/phpstan analyse src --level=4", "phpstan-global": "~/.config/composer/vendor/bin/phpstan analyse src --level=4"}, "extra": {"scripts-descriptions": {"ci": "Run tests before CI", "phpcs": "Checks coding styles (PSR2) of file/dir, which you must supply. To check all, supply 'src'", "phpcbf": "Fix coding styles (PSR2) of file/dir, which you must supply. To fix all, supply 'src'", "cs-fix-all": "Fix the coding style of all the source files, to comply with the PSR-2 coding standard", "cs-fix": "Fix the coding style of a PHP file or directory, which you must specify.", "test": "Launches the preconfigured PHPUnit"}}, "autoload": {"psr-4": {"ExecWithFallback\\": "src/"}}, "autoload-dev": {"psr-4": {"ExecWithFallback\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.bitwise-it.dk/contact", "role": "Project Author"}], "require": {"php": "^5.6 | ^7.0 | ^8.0"}, "suggest": {"php-stan/php-stan": "Suggested for dev, in order to analyse code before committing"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "3.*"}, "config": {"sort-packages": true}}