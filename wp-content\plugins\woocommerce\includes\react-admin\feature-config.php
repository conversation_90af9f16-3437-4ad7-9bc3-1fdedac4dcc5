<?php
// WARNING: Do not directly edit this file.
// This file is auto-generated as part of the build process and things may break.
if ( ! function_exists( 'wc_admin_get_feature_config' ) ) {
	function wc_admin_get_feature_config() {
		return array(
			'activity-panels' => true,
			'analytics' => true,
			'product-block-editor' => true,
			'product-data-views' => false,
			'experimental-blocks' => false,
			'coming-soon-newsletter-template' => false,
			'coupons' => true,
			'core-profiler' => true,
			'customize-store' => true,
			'customer-effort-score-tracks' => true,
			'import-products-task' => true,
			'experimental-fashion-sample-products' => true,
			'shipping-smart-defaults' => true,
			'shipping-setting-tour' => true,
			'homescreen' => true,
			'marketing' => true,
			'minified-js' => false,
			'mobile-app-banner' => true,
			'onboarding' => true,
			'onboarding-tasks' => true,
			'pattern-toolkit-full-composability' => true,
			'point-of-sale' => false,
			'product-pre-publish-modal' => false,
			'product-custom-fields' => true,
			'remote-inbox-notifications' => true,
			'remote-free-extensions' => true,
			'payment-gateway-suggestions' => true,
			'printful' => true,
			'settings' => false,
			'shipping-label-banner' => true,
			'subscriptions' => true,
			'store-alerts' => true,
			'transient-notices' => true,
			'woo-mobile-welcome' => true,
			'wc-pay-promotion' => true,
			'wc-pay-welcome-page' => true,
			'async-product-editor-category-field' => false,
			'launch-your-store' => true,
			'product-editor-template-system' => false,
			'use-wp-horizon' => false,
			'add-to-cart-with-options-stepper-layout' => true,
			'blockified-add-to-cart' => false,
			'disable-core-profiler-fallback' => true,
		);
	}
}
