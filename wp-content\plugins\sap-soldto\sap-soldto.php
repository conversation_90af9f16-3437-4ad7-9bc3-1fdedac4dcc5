<?php
// DEBUG: catch any fatal errors and log them
add_action( 'init', function() {
    register_shutdown_function( function() {
        $err = error_get_last();
        if ( $err && in_array( $err['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR] ) ) {
            error_log( "🔥 Fatal error in SAP-SoldTo API: " . print_r( $err, true ) );
            error_log( print_r( debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS ), true ) );
        }
    });
});

// DEBUG: intercept wp_die() and log a trace
add_filter( 'wp_die_handler', function() {
    return function( $message, $title = '', $args = [] ) {
        error_log( "⚠️ wp_die fired: {$message}" );
        error_log( print_r( debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS ), true ) );
        // then call the normal handler so WP will still die
        _default_wp_die_handler( $message, $title, $args );
    };
});

/**
 * Plugin Name: SAP Sold TO API
 * Plugin URI: https://atakinteractive.com
 * Description: Creates WP users from JSON and adds all SoldTo/billing fields as user meta.
 * Version:     1.8
 * Author:      ATAK Interactive
 * Author URI: https://atakinteractive.com
 * Requires at least: 5.0
 * Tested up to: 6.7
 * Requires PHP: 7.4
 * Network: false
 * License: GPL v2 or later
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Debug: Log when plugin loads
error_log( "🔍 SAP-SoldTo plugin loaded at " . current_time( 'mysql' ) );

// Debug: Check if WooCommerce is active
add_action( 'init', function() {
    error_log( "🔍 SAP-SoldTo init hook - WooCommerce active: " . ( class_exists( 'WooCommerce' ) ? 'YES' : 'NO' ) );
} );

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-soldto', [
        'methods'             => 'POST',
        'callback'            => 'cuc_create_customer_endpoint',
        'permission_callback' => 'sap_soldto_permission_check',
    ] );

    // Add a simple GET endpoint for testing
    register_rest_route( 'wc/v3', '/sap-soldto-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            error_log( "🔍 SAP-SoldTo TEST endpoint called" );
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP SoldTo API is active',
                'timestamp' => current_time( 'mysql' ),
                'wordpress_version' => get_bloginfo( 'version' ),
                'woocommerce_active' => class_exists( 'WooCommerce' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true', // Keep test endpoint open
    ] );
} );

/**
 * Permission callback for SAP SoldTo API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_soldto_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP SoldTo API: Checking WordPress REST API authentication" );

    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();

    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP SoldTo API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }

    error_log( "🔍 SAP SoldTo API: Authenticated user: {$user->ID} ({$user->user_login})" );

    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP SoldTo API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];

    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP SoldTo API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP SoldTo API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Permission callback with detailed logging
 */
function cuc_check_permissions( WP_REST_Request $request ) {
    error_log( "🔍 SAP-SoldTo permission check started" );

    // Get current user info
    $current_user = wp_get_current_user();
    error_log( "🔍 Current user ID: " . $current_user->ID );
    error_log( "🔍 Current user roles: " . print_r( $current_user->roles, true ) );

    // Check if user is logged in
    if ( ! is_user_logged_in() ) {
        error_log( "❌ User not logged in, checking for API authentication" );

        // Check for basic auth or other authentication methods
        $auth_header = $request->get_header( 'authorization' );
        if ( $auth_header ) {
            error_log( "🔍 Authorization header present: " . substr( $auth_header, 0, 20 ) . "..." );

            // Try to authenticate with basic auth
            if ( strpos( $auth_header, 'Basic ' ) === 0 ) {
                $credentials = base64_decode( substr( $auth_header, 6 ) );
                if ( $credentials && strpos( $credentials, ':' ) !== false ) {
                    list( $username, $password ) = explode( ':', $credentials, 2 );
                    $user = wp_authenticate( $username, $password );

                    if ( ! is_wp_error( $user ) ) {
                        wp_set_current_user( $user->ID );
                        error_log( "✅ Basic auth successful for user: " . $user->user_login );
                    } else {
                        error_log( "❌ Basic auth failed: " . $user->get_error_message() );
                    }
                }
            }
        }
    }

    // Re-check current user after potential authentication
    $current_user = wp_get_current_user();

    // Check capabilities
    $can_create_users = current_user_can( 'create_users' );
    $can_manage_wc = current_user_can( 'manage_woocommerce' );
    $is_admin = current_user_can( 'manage_options' );

    error_log( "🔍 Capability check - create_users: " . ( $can_create_users ? 'YES' : 'NO' ) );
    error_log( "🔍 Capability check - manage_woocommerce: " . ( $can_manage_wc ? 'YES' : 'NO' ) );
    error_log( "🔍 Capability check - manage_options: " . ( $is_admin ? 'YES' : 'NO' ) );

    // Allow if user has any of these capabilities
    if ( $can_create_users || $can_manage_wc || $is_admin ) {
        error_log( "✅ Permission granted" );
        return true;
    }

    // For development/testing - you can temporarily allow all requests
    // REMOVE THIS IN PRODUCTION!
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( "⚠️ DEBUG MODE: Allowing request without proper authentication" );
        return true;
    }

    error_log( "❌ Permission denied" );
    return false;
}

/**
 * Endpoint logic: create user, then add ALL meta after clearing caches.
 */
function cuc_create_customer_endpoint( WP_REST_Request $request ) {
    // FIRST - Log that the endpoint was called
    error_log( "🚀 SAP-SoldTo API ENDPOINT CALLED!" );
    error_log( "🚀 Request method: " . $request->get_method() );
    error_log( "🚀 Request URL: " . $request->get_route() );

    $data = $request->get_json_params();

    // Log the incoming data for debugging
    error_log( "🔍 SAP-SoldTo API received data: " . print_r( $data, true ) );

    // — Enhanced Validation —
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ No valid JSON data received" );
        return new WP_Error( 'invalid_data', 'No valid JSON data received', [ 'status'=>400 ] );
    }

    if ( empty( $data['soldTo'] ) || ! is_array( $data['soldTo'] ) ) {
        error_log( "❌ soldTo data is missing or invalid" );
        return new WP_Error( 'missing_soldto', 'soldTo data is required and must be an object', [ 'status'=>400 ] );
    }

    if ( empty( $data['soldTo']['customerId'] ) ) {
        error_log( "❌ customerId is missing" );
        return new WP_Error( 'missing_customer_id', 'soldTo.customerId is required', [ 'status'=>400 ] );
    }
    if ( empty( $data['soldTo']['email'] ) || ! is_email( $data['soldTo']['email'] ) ) {
        error_log( "❌ Invalid email: " . ( $data['soldTo']['email'] ?? 'empty' ) );
        return new WP_Error( 'invalid_email', 'A valid soldTo.email is required', [ 'status'=>400 ] );
    }

    // — Create user —
    $email    = sanitize_email( $data['soldTo']['email'] );
    $username = sanitize_user( $email, true );

    error_log( "🔍 Attempting to create user with email: {$email}, username: {$username}" );

    if ( username_exists( $username ) || email_exists( $email ) ) {
        error_log( "❌ User already exists: {$email}" );
        return new WP_Error( 'user_exists', 'User/email already exists', [ 'status'=>400 ] );
    }

    $password = wp_generate_password( 12, false );
    error_log( "🔍 Generated password, about to call wp_insert_user" );

    $user_data = [
        'user_login' => $username,
        'user_pass'  => $password,
        'user_email'=> $email,
        'role'       => 'b2b_customer',
    ];
    error_log( "🔍 User data for creation: " . print_r( $user_data, true ) );

    // — Completely disable all user_register hooks temporarily —
    error_log( "🔍 Completely disabling ALL user_register hooks" );
    global $wp_filter;
    $original_user_register_hooks = null;

    if ( isset( $wp_filter['user_register'] ) ) {
        $original_user_register_hooks = $wp_filter['user_register'];
        unset( $wp_filter['user_register'] );
        error_log( "🔍 All user_register hooks temporarily removed" );
    }

    $user_id = wp_insert_user( $user_data );
    error_log( "🔍 wp_insert_user returned: " . print_r( $user_id, true ) );

    // — Restore hooks immediately after wp_insert_user, regardless of result —
    if ( $original_user_register_hooks !== null ) {
        $wp_filter['user_register'] = $original_user_register_hooks;
        error_log( "🔍 Hooks restored after wp_insert_user call" );
    }

    if ( is_wp_error( $user_id ) ) {
        error_log( "❌ Failed to create user: " . $user_id->get_error_message() );
        error_log( "❌ Error data: " . print_r( $user_id->get_error_data(), true ) );
        return $user_id;
    }

    if ( empty( $user_id ) || ! is_numeric( $user_id ) ) {
        error_log( "❌ wp_insert_user returned invalid user ID: " . var_export( $user_id, true ) );
        return new WP_Error( 'user_creation_failed', 'Failed to create user - invalid user ID returned', [ 'status' => 500 ] );
    }

    error_log( "✅ User created successfully with ID: {$user_id}" );

    // — Ensure WooCommerce customer setup —
    error_log( "🔍 Checking WooCommerce customer setup" );
    if ( class_exists( 'WC_Customer' ) ) {
        error_log( "🔍 WC_Customer class exists, creating customer object" );
        try {
            $customer = new WC_Customer( $user_id );
            if ( $customer->get_id() ) {
                error_log( "✅ WooCommerce customer object created for user {$user_id}" );
            } else {
                error_log( "⚠️ WooCommerce customer object created but no ID returned" );
            }
        } catch ( Exception $e ) {
            error_log( "⚠️ WooCommerce customer creation warning: " . $e->getMessage() );
            error_log( "⚠️ Exception trace: " . $e->getTraceAsString() );
        }
    } else {
        error_log( "⚠️ WC_Customer class not found - WooCommerce may not be active" );
    }

    // — Clear caches so meta writes hit the DB —
    error_log( "🔍 Clearing caches before metadata operations" );
    if ( function_exists('clean_user_cache') ) {
        clean_user_cache( $user_id );
        error_log( "🔍 clean_user_cache called" );
    }
    if ( function_exists('wp_cache_flush') ) {
        wp_cache_flush();
        error_log( "🔍 wp_cache_flush called" );
    }

    // Additional cache clearing for user meta
    wp_cache_delete( $user_id, 'user_meta' );
    wp_cache_delete( $user_id, 'users' );
    error_log( "🔍 Additional cache clearing completed" );

    // Force a small delay to ensure database writes complete
    usleep( 100000 ); // 0.1 seconds
    error_log( "🔍 Cache clearing and delay completed, proceeding to metadata" );

    // — Add SoldTo meta —
    error_log( "🔍 Starting metadata operations for user {$user_id}" );

    try {
        $sold = $data['soldTo'];
        error_log( "🔍 Adding SoldTo metadata for user {$user_id}" );
        error_log( "🔍 SoldTo data: " . print_r( $sold, true ) );

    // Define all required SoldTo fields with correct WordPress metadata keys
    $soldto_fields = [
        '_customer'     => $sold['customerId'] ?? '',
        '_companycode'  => $sold['companyCode'] ?? '',
        '_country'      => $sold['countryCode'] ?? '',
        '_pricegroup'   => $sold['priceGroup'] ?? ''
    ];

    error_log( "🔍 Processing SoldTo fields: " . print_r( $soldto_fields, true ) );

    // Add each field using the correct WordPress metadata keys
    foreach ( $soldto_fields as $meta_key => $meta_value ) {
        if ( !empty( $meta_value ) ) {
            $sanitized_value = sanitize_text_field( $meta_value );
            error_log( "🔍 Adding {$meta_key}: '{$sanitized_value}'" );

            $result = add_user_meta( $user_id, $meta_key, $sanitized_value );
            if ( $result === false ) {
                error_log( "❌ add_user_meta failed for {$meta_key}, trying update..." );
                $result = update_user_meta( $user_id, $meta_key, $sanitized_value );
                error_log( "🔄 update_user_meta result for {$meta_key}: " . var_export($result, true) );
            } else {
                error_log( "✅ Successfully added {$meta_key}: " . var_export($result, true) );
            }

            // Immediate verification
            $saved_value = get_user_meta( $user_id, $meta_key, true );
            if ( $saved_value === $sanitized_value ) {
                error_log( "✅ Verified {$meta_key} saved correctly: '{$saved_value}'" );
            } else {
                error_log( "❌ Verification failed for {$meta_key}. Expected: '{$sanitized_value}', Got: '{$saved_value}'" );

                // Try direct database insert as fallback
                global $wpdb;
                $db_result = $wpdb->insert(
                    $wpdb->usermeta,
                    [
                        'user_id' => $user_id,
                        'meta_key' => $meta_key,
                        'meta_value' => $sanitized_value
                    ]
                );

                if ( $db_result ) {
                    error_log( "✅ Direct database insert successful for {$meta_key}" );
                } else {
                    error_log( "❌ Direct database insert failed for {$meta_key}: " . $wpdb->last_error );
                }
            }

        } else {
            error_log( "⚠️ Skipping empty field: {$meta_key}" );
        }
    }

    // — Add billingAddress meta —
    error_log( "🔍 Adding billing address metadata for user {$user_id}" );
    $bill = is_array( $data['billingAddress'] ) ? $data['billingAddress'] : [];
    error_log( "🔍 Billing data: " . print_r( $bill, true ) );

    // Add billing company
    if ( !empty( $bill['company'] ) ) {
        $company = sanitize_text_field( $bill['company'] );
        $result = add_user_meta( $user_id, 'billing_company', $company );
        if ( $result === false ) {
            $result = update_user_meta( $user_id, 'billing_company', $company );
            error_log( "🔄 Updated billing_company: " . var_export($result, true) );
        } else {
            error_log( "✅ Added billing_company: " . var_export($result, true) );
        }
    }

    // Add billing address fields
    $addr = is_array( $bill['address'] ) ? $bill['address'] : [];
    $billing_fields = [
      'billing_address_1' => $addr['line1']         ?? '',
      'billing_address_2' => $addr['line2']         ?? '',
      'billing_city'      => $addr['city']          ?? '',
      'billing_postcode'  => $addr['postcode']      ?? '',
      'billing_country'   => $addr['countryRegion'] ?? '',
      'billing_state'     => $addr['stateCounty']   ?? '',
    ];

    foreach ( $billing_fields as $meta_key => $meta_val ) {
        if ( !empty( $meta_val ) ) {
            $sanitized_value = sanitize_text_field( $meta_val );
            $result = add_user_meta( $user_id, $meta_key, $sanitized_value );
            if ( $result === false ) {
                $result = update_user_meta( $user_id, $meta_key, $sanitized_value );
                error_log( "🔄 Updated {$meta_key}: " . var_export($result, true) );
            } else {
                error_log( "✅ Added {$meta_key}: " . var_export($result, true) );
            }
        }
    }

    // — Add shiptos array —
    error_log( "🔍 Adding shiptos metadata for user {$user_id}" );
    $shiptos = is_array( $data['shiptos'] ) ? $data['shiptos'] : [];
    error_log( "🔍 Shiptos data: " . print_r( $shiptos, true ) );

    if ( !empty( $shiptos ) ) {
        // Clean and sanitize the shiptos array
        $sanitized_shiptos = array_map( function( $shipto ) {
            return trim( sanitize_text_field( $shipto ) );
        }, $shiptos );

        // Remove any empty values
        $sanitized_shiptos = array_filter( $sanitized_shiptos );

        error_log( "🔍 Sanitized shiptos: " . print_r( $sanitized_shiptos, true ) );

        // Store as _shiptos to match your field naming convention
        $result = add_user_meta( $user_id, '_shiptos', $sanitized_shiptos );
        if ( $result === false ) {
            error_log( "❌ add_user_meta failed for _shiptos, trying update..." );
            $result = update_user_meta( $user_id, '_shiptos', $sanitized_shiptos );
            error_log( "🔄 update_user_meta result for _shiptos: " . var_export($result, true) );
        } else {
            error_log( "✅ Successfully added _shiptos: " . var_export($result, true) );
        }

        // Immediate verification
        $saved_shiptos = get_user_meta( $user_id, '_shiptos', true );
        if ( !empty( $saved_shiptos ) && is_array( $saved_shiptos ) ) {
            error_log( "✅ Verified _shiptos saved correctly: " . print_r( $saved_shiptos, true ) );
        } else {
            error_log( "❌ Verification failed for _shiptos. Got: " . print_r( $saved_shiptos, true ) );

            // Try direct database insert as fallback
            global $wpdb;
            $serialized_shiptos = maybe_serialize( $sanitized_shiptos );
            $db_result = $wpdb->insert(
                $wpdb->usermeta,
                [
                    'user_id' => $user_id,
                    'meta_key' => '_shiptos',
                    'meta_value' => $serialized_shiptos
                ]
            );

            if ( $db_result ) {
                error_log( "✅ Direct database insert successful for _shiptos" );
            } else {
                error_log( "❌ Direct database insert failed for _shiptos: " . $wpdb->last_error );
            }
        }
    } else {
        error_log( "⚠️ No shiptos data provided or empty array" );
    }

    // — Comprehensive metadata verification —
    error_log( "🔍 Comprehensive metadata verification for user {$user_id}" );

    // Get ALL user meta to see what was actually saved
    $all_meta = get_user_meta( $user_id );
    error_log( "🔍 ALL user metadata: " . print_r( $all_meta, true ) );

    $verification_fields = ['_customer', '_companycode', '_country', '_pricegroup', '_shiptos'];
    $verification_results = [];

    foreach ( $verification_fields as $field ) {
        $saved_value = get_user_meta( $user_id, $field, true );
        $verification_results[$field] = $saved_value;

        if ( !empty( $saved_value ) ) {
            if ( $field === '_shiptos' && is_array( $saved_value ) ) {
                error_log( "✅ Verified {$field}: " . print_r( $saved_value, true ) );
                $verification_results[$field] = $saved_value; // Keep as array for response
            } else {
                error_log( "✅ Verified {$field}: '{$saved_value}'" );
            }
        } else {
            error_log( "❌ CRITICAL: {$field} was not saved for user {$user_id}" );
        }
    }

    // Also verify user role
    $user = get_user_by( 'ID', $user_id );
    if ( $user && in_array( 'b2b_customer', $user->roles ) ) {
        error_log( "✅ Verified user role: b2b_customer" );
    } else {
        error_log( "❌ User role verification failed. Current roles: " . print_r( $user->roles ?? [], true ) );
    }

        error_log( "✅ All metadata operations completed successfully for user {$user_id}" );

    } catch ( Exception $e ) {
        error_log( "❌ FATAL ERROR in metadata operations: " . $e->getMessage() );
        error_log( "❌ Exception trace: " . $e->getTraceAsString() );
        return new WP_Error( 'metadata_error', 'Failed to save user metadata: ' . $e->getMessage(), [ 'status' => 500 ] );
    }

    // — Response —
    return rest_ensure_response([
        'success'  => true,
        'user_id'  => $user_id,
        'username' => $username,
        'password' => $password,
        'role'     => 'b2b_customer',
        'metadata_verification' => $verification_results,
        'user_roles' => $user->roles ?? []
    ]);
}
