<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" bootstrap="vendor/autoload.php" backupGlobals="false" backupStaticAttributes="false" colors="true" verbose="true" convertErrorsToExceptions="true" convertNoticesToExceptions="true" convertWarningsToExceptions="true" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
  <coverage>
    <include>
      <directory suffix=".php">src/</directory>
    </include>
    <report>
      <clover outputFile="build/logs/clover.xml"/>
      <html outputDirectory="build/coverage"/>
      <text outputFile="build/coverage.txt"/>
    </report>
  </coverage>
  <testsuites>
    <testsuite name=":vendor Test Suite">
      <directory suffix="Test.php">./tests/</directory>
    </testsuite>
  </testsuites>
  <logging>
    <junit outputFile="build/report.junit.xml"/>
  </logging>
</phpunit>
