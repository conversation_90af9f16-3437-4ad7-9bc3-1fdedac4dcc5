<?php

global $wpdb;

register_activation_hook(__FILE__, 'my_plugin_activation');
function my_plugin_activation()
{
    ob_start();
    create_price_list_table();
    create_price_code_list();
    create_price_group();
    create_price_code_group_relation();
    create_contract_pricing();
    ob_end_clean();
}

function create_price_list_table()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'price_list';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            product_sku VARCHAR(255) NOT NULL,
            price_list_value VARCHAR(255) DEFAULT NULL,
            currency VARCHAR(255) DEFAULT NULL,
            brand VARCHAR(255) DEFAULT NULL,
            description VARCHAR(255) DEFAULT NULL,
            price DECIMAL(10, 3) NOT NULL,
            price_code_id INT UNSIGNED NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE (product_sku)
        ) CHARACTER SET utf8 COLLATE utf8_general_ci;";
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

function create_price_code_list()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'price_code_list';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            price_code_title VARCHAR(255) NOT NULL
        ) CHARACTER SET utf8 COLLATE utf8_general_ci;";
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

function create_price_group()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'price_group';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            price_group_title VARCHAR(255) NOT NULL
        ) CHARACTER SET utf8 COLLATE utf8_general_ci;";
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

function create_price_code_group_relation()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'price_code_group_relation';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            price_code_id INT UNSIGNED NOT NULL,
            price_group_id INT UNSIGNED NOT NULL,
            status VARCHAR(255) NULL
        ) CHARACTER SET utf8 COLLATE utf8_general_ci;";
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}


function create_contract_pricing()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'contract_pricing';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            id_row VARCHAR(255) DEFAULT NULL,
            customer_id INT UNSIGNED NOT NULL,
            customer_name VARCHAR(255) DEFAULT NULL,
            product_sku VARCHAR(255) DEFAULT NULL,
            description VARCHAR(255) DEFAULT NULL,
            new_price FLOAT(10, 3) NOT NULL,
            currency VARCHAR(255) DEFAULT NULL,
            UNIQUE (id_row)
        ) CHARACTER SET utf8 COLLATE utf8_general_ci;";
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}