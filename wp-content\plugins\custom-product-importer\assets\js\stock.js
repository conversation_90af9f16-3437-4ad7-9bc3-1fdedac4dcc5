jQuery(document).ready(function ($) {
    console.log("Stock script loaded.");
    $("#btn-stock-excel-file").on("click", function () {
        const fileInput = document.getElementById("stock-excel-file").files[0];
        if (!fileInput) {
            alert("Please select a file!");
            return;
        }

        $("#stock-upload-pan").show();

        const reader = new FileReader();
        reader.onload = function (event) {
            const data = new Uint8Array(event.target.result);
            const workbook = XLSX.read(data, { type: "array" });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // Validate headers
            const headers = jsonData[0];
            if (
                headers[0] !== "Production Plant #" ||
                headers[1] !== "Material #" ||
                headers[2] !== "Net Available Quantity"
            ) {
                alert("This isn't a valid file.");
                $("#stock-upload-pan").hide();
                return;
            }

            // Process data
            const rows = jsonData.slice(1); // Ignore header row
            // const validRows = rows.filter(row => row[0] && row[1] && row[2]); // Skip empty rows
            const validRows = rows // Skip empty rows

            if (validRows.length === 0) {
                alert("No valid rows found.");
                $("#stock-upload-pan").hide();
                return;
            }

            // Prepare data for batch processing
            const batchSize = 500;
            const totalRows = validRows.length;
            // console.log(rows.length);
            // return;
            let currentIndex = 0;
            let currentBatchIndex = 0;

            function updateProgress() {
                const percent = Math.round((currentBatchIndex / totalRows) * 100);
                $("#stock-progress-text").text(percent + "%");
                $("#stock-progress-bar").css("width", percent + "%");
            }

            function processBatch() {
                const batch = validRows.slice(currentIndex, currentIndex + batchSize);
                const skus = batch.map(row => row[1]); // Extract 2nd column (Material #)
                const saveData = [];
                // Send SKUs to backend to fetch product IDs
                $.ajax({
                    url: ajax_obj.ajax_url, // WordPress AJAX endpoint
                    method: "POST",
                    data: {
                        action: "fetch_product_ids",
                        skus: skus,
                    },
                    success: function (response) {
                        // const productMap = JSON.parse(response);
                        const productMap = response;
                        const testProductMap = productMap.filter(item => item.product_id == -1);
                        console.log(testProductMap);
                        

                        batch.forEach((row, index) => {
                            const sku = row[1];
                            const quantity = Math.max(0, parseInt(row[2], 10)); // Third column
                            const productInfo = productMap.find(item => item.product_sku === sku);
                            if (productInfo && productInfo.product_id !== -1) {
                                const meta_stock = row[0] === "3090" ? "us" : "eu";
                                saveData.push({
                                    product_id: productInfo.product_id,
                                    meta_stock: "_stock_" + meta_stock,
                                    meta_shuffix: meta_stock,
                                    stock_count: quantity,
                                    additional_meta: [
                                        { key: `_manage_stock_${meta_stock}`, value: "yes" },
                                        { key: `_stock_status_${meta_stock}`, value: "instock" },
                                        { key: `_backorders_${meta_stock}`, value: "no" },
                                        { key: `_low_stock_amount_${meta_stock}`, value: "0" }
                                    ]
                        
                                });

                            }
                        });
                        
                        console.log(saveData);
                        // Send save data to backend
                        $.ajax({
                            url: ajax_obj.ajax_url,
                            method: "POST",
                            data: {
                                action: "save_stock_data",
                                data: JSON.stringify(saveData),
                            },
                            success: function (saveResponse) {
                                // Update progress and process next batch
                                currentIndex += batchSize;
                                currentBatchIndex += saveData.length;
                                console.log(currentBatchIndex);
                                updateProgress();

                                if (currentIndex < totalRows) {
                                    processBatch();
                                } else {
                                    alert("Upload completed!");
                                    $("#stock-upload-pan").hide();
                                }
                            },
                            error: function () {
                                alert("Error saving batch.");
                                $("#stock-upload-pan").hide();
                            }
                        });
                    },
                    error: function () {
                        alert("Error fetching product IDs.");
                        $("#stock-upload-pan").hide();
                    }
                });
            }

            // Start processing batches
            processBatch();
        };

        reader.readAsArrayBuffer(fileInput);
    });
});
