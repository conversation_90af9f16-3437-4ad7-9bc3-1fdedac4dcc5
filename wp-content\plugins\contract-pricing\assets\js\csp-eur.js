jQuery(document).ready(function ($) {
    loadCustomers();
    loadProducts();
    // Load the data using DataTable
    var table = $('#pricingTableEur').DataTable({
        ajax: {
            url: csp_eur_ajax_object.ajaxurl,
            type: 'POST',
            data: { action: 'get_pricing_data_eur' }
        }
    });

    // Open modal for adding new pricing
    $('#addNewPricingEur').on('click', function () {
        $('#pricingModalEur').show();
        $('#row_id_eur').val('');
    });

    $('#closeModalEur').on('click', function () {
        $('#pricingModalEur').hide();
    });

    // Save pricing data
    $('#pricingFormEur').on('submit', function (e) {
        e.preventDefault();
        var action = $('#row_id_eur').val() ? 'edit_pricing_eur' : 'save_pricing_eur';
        $.post(csp_eur_ajax_object.ajaxurl, $(this).serialize() + '&action=' + action, function (response) {
            if (response.success) {
                $('#pricingModalEur').hide();
                table.ajax.reload();
            } else {
                alert(response.data.message);
            }
        });
    });

    // Load customers for dropdown
    function loadCustomers() {
        $.post(csp_eur_ajax_object.ajaxurl, { action: 'get_customers' }, function (customers) {
            $('#customerEur').html('');
            $.each(customers, function (i, customer) {
                $('#customerEur').append('<option value="' + customer.id + '">' + customer.name + '</option>');
            });
        });
    }

    // Load products for dropdown
    function loadProducts() {
        $.post(csp_eur_ajax_object.ajaxurl, { action: 'get_products' }, function (products) {
            $('#productEur').html('');
            $.each(products, function (i, product) {
                $('#productEur').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
        });
    }

    // Delete pricing data
    $('#pricingTableEur').on('click', '.deletePricing', function () {
        if (confirm('Are you sure you want to delete this entry?')) {
            var id = $(this).data('id');
            $.post(csp_eur_ajax_object.ajaxurl, { action: 'delete_pricing_eur', id: id }, function () {
                table.ajax.reload();
            });
        }
    });

    // Edit pricing data)
    $('#pricingTableEur').on('click', '.editPricing', function () {
        var id = $(this).data('id');
        $.post(csp_eur_ajax_object.ajaxurl, { action: 'get_pricing_by_id_eur', id: id }, function (response) {
            if (response.success) {
                var data = response.data;
                $('#row_id_eur').val(data.id);
                loadCustomersById(data.customer_id);
                loadProductsById(data.product_sku);
                $('#new_price_eur').val(data.new_price);
                $('#pricingModalEur').show();
            } else {
                alert(response.data.message);
            }
        });
    });

    function loadCustomersById(selectedCustomerId) {
        $.post(csp_eur_ajax_object.ajaxurl, { action: 'get_customers_eur' }, function (customers) {
            $('#customerEur').html('');  // Clear the dropdown
            $.each(customers, function (i, customer) {
                if(customer.id == selectedCustomerId) {
                    $('#customerEur').append('<option value="' + customer.id + '" selected>' + customer.name + '</option>');
                } else {
                    $('#customerEur').append('<option value="' + customer.id + '">' + customer.name + '</option>');
                }
            });
        });
    }

    function loadProductsById(selectedProductSku) {
        $.post(csp_eur_ajax_object.ajaxurl, { action: 'get_products_eur' }, function (products) {
            $('#productEur').html('');  // Clear the dropdown
            $.each(products, function (i, product) {
                $('#productEur').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
            if (selectedProductSku) {
                $('#productEur').val(selectedProductSku);
            }
        });
    }
    $('.dataTables_length select').css({ 'width': '100px' });
    $("#closeModalEur").on("click", function () {
        $('#pricingModal').hide();
    });
    $("#closeEur").on("click", function () {
        $('#pricingModalEur').hide();
    });

});

