<?php
function csp_enqueue_scripts()
{
    wp_enqueue_script('jquery');
    wp_enqueue_script('datatable', 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js', array('jquery'), '1.11.5', true);
    wp_enqueue_style('datatable-css', 'https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css');
    wp_enqueue_script('csp-main-js', plugins_url('/assets/js/csp-main.js', __FILE__), array('jquery'), '1.0', true);
    wp_enqueue_style('csp-main-css', plugins_url('/assets/css/csp-main.css', __FILE__));
    wp_localize_script('csp-main-js', 'csp_ajax_object', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('csp_nonce')
    ));
}

add_action('admin_enqueue_scripts', 'csp_enqueue_scripts');
function csp_usd_page()
{
?>
    <div class="wrap">
        <h1 style="margin-bottom: 20px;">Contract Pricing USD</h1>
        <button id="addNewPricing" class="button-primary" style="margin-bottom: 20px;">Add New</button>
        <table id="pricingTable" class="display">
            <thead>
                <tr>
                    <th>Customer ID</th>
                    <th>Customer Name</th>
                    <th>Product SKU</th>
                    <th>Description</th>
                    <th>Price</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>

    <!-- Add/Edit Modal -->
    <div id="pricingModal" style="display: none;">
        <span id="closeModal">X</span>
        <form id="pricingForm">
            <label for="customer">Customer</label>
            <select id="customer" name="customer"></select><br>

            <label for="product">Product</label>
            <select id="product" name="product"></select><br>

            <label for="new_price">Sales Price</label>
            <input type="text" id="new_price" name="new_price" /><br>

            <input type="hidden" id="row_id" name="row_id">
            <div style="display: flex; justify-content:space-between;">
                <button type="submit" class="button-primary btn-submit" id="savePricing">Save</button>
                <button type="button" class="button-primary btn-submit" id="close">Close</button>
            </div>
        </form>
    </div>
<?php
}

// AJAX handler to get pricing data
function csp_get_pricing_data()
{
    global $wpdb;
    $table = $wpdb->prefix . 'contract_pricing';
    $results = $wpdb->get_results("SELECT * FROM $table", ARRAY_A);

    $data = array();
    foreach ($results as $row) {
        $data[] = array(
            $row['customer_id'],
            $row['customer_name'],  // Assuming customer_name can be found from wp_users table
            $row['product_sku'],
            $row['description'],
            $row['new_price'],
            '<button class="editPricing" data-id="' . $row['id'] . '">Edit</button> <button class="deletePricing" data-id="' . $row['id'] . '">Delete</button>'
        );
    }

    wp_send_json(array('data' => $data));
}

add_action('wp_ajax_get_pricing_data', 'csp_get_pricing_data');

// AJAX handler to save new pricing
function csp_save_pricing()
{
    global $wpdb;

    $customer_id = $_POST['customer'];

    $product_sku = $_POST['product'];
    $new_price = $_POST['new_price'];

    // Check if pricing already exists
    $table = $wpdb->prefix . 'contract_pricing';
    $exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM $table WHERE customer_id = %d AND product_sku = %s", $customer_id, $product_sku));

    if ($exists) {
        wp_send_json_error(array('message' => 'Already exist!'));
    } else {
        $users = get_users(array(
            'meta_key' => '_customer',
            'meta_value' => $customer_id,
            'number' => 1 // Limit to 1 result, as we are assuming you want a single user
        ));
        
        // Check if the user is found
        if (!empty($users)) {
            $user = $users[0];
            $disp_name = $user->display_name; // Fetch the display name
            
            // Check if the display name is retrieved
            if($disp_name) {
                $table_price = $wpdb->prefix . 'price_list';
                $products = $wpdb->get_results($wpdb->prepare(
                    "SELECT description FROM $table_price WHERE product_sku = %s", $product_sku
                ), ARRAY_A);
                
                // Check if product exists and description is found
                if (!empty($products)) {
                    $description = $products[0]["description"];
                    
                    // Insert data into the price_list table
                    $inserted = $wpdb->insert($table, array(
                        'customer_id' => $customer_id,
                        'customer_name' => $disp_name,
                        'product_sku' => $product_sku,
                        'description' => $description,
                        'new_price' => $new_price
                    ));
                    if($inserted){
                        wp_send_json_success(["success"=>true]);
                    } else {
                        wp_send_json_success(["success"=>false]);
                    }
                } 
            } 
        } 
        // Insert new data

    }
}

add_action('wp_ajax_save_pricing', 'csp_save_pricing');

// AJAX handler to delete pricing
function csp_delete_pricing()
{
    global $wpdb;
    $row_id = intval($_POST['id']);
    $table = $wpdb->prefix . 'contract_pricing';

    $wpdb->delete($table, array('id' => $row_id));

    wp_send_json_success();
}

add_action('wp_ajax_delete_pricing', 'csp_delete_pricing');

// AJAX handler to get product list for modal dropdown
function csp_get_products()
{
    global $wpdb;
    $cache_key = 'csp_products_cache';
    $cached_products = get_transient($cache_key);
    $products = [];
    if ($cached_products === false) {
        $table = $wpdb->prefix . 'price_list';
        $products = $wpdb->get_results("SELECT product_sku FROM $table", ARRAY_A);

        // Cache the results for 12 hours
        set_transient($cache_key, $products, 12 * HOUR_IN_SECONDS);
    } else {
        $products = $cached_products;
    }

    wp_send_json($products);
}

add_action('wp_ajax_get_products', 'csp_get_products');

// AJAX handler to get customers for modal dropdown
function csp_get_customers()
{
    $cache_key = 'csp_customers_cache';
    $cached_customers = get_transient($cache_key);
    $customer_data = [];

    if ($cached_customers === false) {
        $customers = get_users(array('role__in' => array('customer', 'subscriber')));
        foreach ($customers as $customer) {
            $customer_data[] = array(
                'id' => get_user_meta($customer->ID, '_customer', true),
                'name' => get_userdata($customer->ID)->display_name
            );
        }
        set_transient($cache_key, $customer_data, 12 * HOUR_IN_SECONDS);
    } else {
        $customer_data = $cached_customers;
    }
    wp_send_json($customer_data);
}

add_action('wp_ajax_get_customers', 'csp_get_customers');


function get_pricing_by_id() {
    global $wpdb;

    $id = intval($_POST['id']);
    $table = $wpdb->prefix . 'contract_pricing';
    $pricing = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id), ARRAY_A);
    if ($pricing) {
        wp_send_json_success($pricing);
    } else {
        wp_send_json_error(array('message' => 'No data found for the selected row.'));
    }
    wp_die();
}
add_action('wp_ajax_get_pricing_by_id', 'get_pricing_by_id');

function edit_pricing() {
    global $wpdb;

    // Retrieve the data from the form submission
    $row_id       = intval($_POST['row_id']);  // The ID of the row being edited
    $customer_id  = sanitize_text_field($_POST['customer']);  // Customer ID from the dropdown
    $product_sku  = sanitize_text_field($_POST['product']);    // Product SKU from the dropdown
    $new_price    = floatval($_POST['new_price']);  // New price value

    // Validate that the required fields are present
    if (empty($row_id) || empty($customer_id) || empty($product_sku) || empty($new_price)) {
        wp_send_json_error(array('message' => 'All fields are required.'));
    }

    // Fetch customer display name based on customer_id (user meta '_customer')
    $users = get_users(array(
        'meta_key'   => '_customer',
        'meta_value' => $customer_id,
        'number'     => 1
    ));

    if (empty($users)) {
        wp_send_json_error(array('message' => 'Invalid customer selected.'));
    }

    $disp_name = $users[0]->display_name;

    // Fetch the product description based on the SKU
    $table_products = $wpdb->prefix . 'price_list';
    $product = $wpdb->get_row($wpdb->prepare("SELECT description FROM $table_products WHERE product_sku = %s", $product_sku), ARRAY_A);

    if (empty($product)) {
        wp_send_json_error(array('message' => 'Invalid product selected.'));
    }

    // Update the data in the contract pricing table
    $table_pricing = $wpdb->prefix . 'contract_pricing';

    $result = $wpdb->update(
        $table_pricing,
        array(
            'customer_id'    => $customer_id,
            'customer_name'  => $disp_name,
            'product_sku'    => $product_sku,
            'description'    => $product['description'],
            'new_price'      => $new_price
        ),
        array('id' => $row_id),  // Where condition to match the row ID
        array('%s', '%s', '%s', '%s', '%f'),
        array('%d')  // Format for the 'id' column
    );

    // Check if the update was successful
    if ($result !== false) {
        wp_send_json_success(array('message' => 'Pricing data updated successfully.'));
    } else {
        wp_send_json_error(array('message' => 'Failed to update pricing data.'));
    }

    wp_die();  // Always call this to terminate the execution properly in AJAX calls
}
add_action('wp_ajax_edit_pricing', 'edit_pricing');