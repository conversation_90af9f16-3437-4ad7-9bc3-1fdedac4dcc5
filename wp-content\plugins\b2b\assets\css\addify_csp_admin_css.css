#toplevel_page_addify-pec img { padding: 0px !important; }


.text_title{ width: 500px; padding: 10px; }

.login_title2 { width: 60%; padding: 5px !important; }


#addify_csp_panel #accordion h3 { background: #d2d5db !important; padding: 10px 27px; border-radius: 0; font-weight: bold; }

.cname{padding: 10px; }

#addify_csp_panel_customer select.sel22,select.sel2{
	width: 100%;
	min-width: 150px;
}

#csp-meta-box div.cdiv{
  width: 100%;
  overflow-x: auto;
}

#csp-meta-box div.cdiv input[type="number"]{
  min-width: 50px !important;
  height: auto;
}
.cnamee{ width: 90px; float: left; }
.cname12{ width: 90px; float: left; margin-left: 10px; }
.cname22{ width: 90px; float: left; margin-left: 10px; }
.cname32{ width: 90px; float: left; margin-left: 10px; }
.cname52{ width: 90px; float: left; margin-left: 10px; }
.cname42{ width: 90px; float: left; margin-left: 10px; }
.cname62{ width: 90px; float: left; margin-left: 10px; }
.cname72{ width: 90px; float: left; margin-left: 10px; }

.cdiv { height: 100% !important; }

#addify_csp_panel table tr { margin-top: 20px; float: left; }

.cspdv { width: auto; background: #d2d5db !important; padding: 5px 10px; border-radius: 0; font-weight: bold; margin-bottom: 15px; cursor: pointer; }

.cspcontainer input[type=number] { min-width: 50px !important; }
.cspcontainer input[type=text] { min-width: 60px !important; }
.cspcontainer input[type=checkbox] { min-width: auto !important; }
.cspcontainer select{min-width: 150px !important;}

#addify_csp_panel input { width: 100% !important; }

.cspdv tr { margin-top: 10px; float: left; }


// Colors
$default: #878d99;
$primary: #409eff;
$sucess: #67c23a;

// Some Reset


.cspcontainer {
	width: 450px;
	height: auto;
}

// Change component color
@mixin states($color: $default) {
	border-color: $color;
	color: rgba(255, 255, 255, 0.9);
	background: $color;

	&:hover {
		border-color: $color;
		color: rgba(255, 255, 255, 0.6);
	}
	&:active {
		color: rgba(255, 255, 255, 0.9);
		border-color: darken($color, 10%);
	}
}

// Collapse / Expand Component
details {
	@include states($default);
	border: 1px solid;
	cursor: pointer;
	transition: background 0.3s;
	border-radius: 4px;
	min-height: 48px;
	max-height: 60px;
	transform-origin: top center;
	transform: rotate(0.1deg);
	transition: all 0.3s;

	::-webkit-details-marker {
		display: none;
	}

	+ details {
		margin-top: 10px;
	}

	p {
		color: #fefefe;
		line-height: 1.7;
		margin: 10px 0 0;
		padding: 0 20px 15px;
	}

	// Primary Style
	&.primary {
		@include states($primary);
	}

	// Success Style
	&.success {
		@include states($sucess);
	}

	// Square Style
	&.square {
		border-radius: 0;
	}

	&[open] {
		transition: all 0.6s;
		min-height: 100px;
		max-height: 200px;
	}
}

summary {
	outline: none;
	font-size: 16px;
	padding: 13px;
	
	&:selection {
		background: transparent;
	}

	&:after {
		margin-top: 2px;
		content: "➕";
		float: left;
		margin-right: 11px;
		text-align: center;
		font-size: 11px;

		[open] & {
			padding: 0 0 12px 0;
			content: "➖";
		}
	}
}

#csp-meta-box { width: 100%; float: left; margin-bottom: 20px; }

.csp_admin_main { width: 100%; float: left; margin-top: 15px; margin-bottom: 15px;}

.csp_admin_main_left{ width: 22%; float: left }

.csp_admin_main_right { width: 77%; float: left }

.csp_admin_main label{ float: left; margin-top: 5px; }

.applied_on_products{ width: 100% !important; }

#applied_on_products, #applied_on_categories{ display: none; }

.all_cats { 
	width: 95%; 
	border: solid 1px #b6b1b1; 
	border-radius: 4px; 
	padding: 10px; 
	float: left;
	max-height: 400px;
	overflow: auto;
}

.par_cat { width: auto; padding: 5px; }

.child_cat{ width: auto; padding:3px 15px; }

.rcname{ width: 150px; float: left; }
.rcname1{ width: 150px; float: left; margin-left: 10px; }
.rcname2{ width: 150px; float: left; margin-left: 10px; }
.rcname3{ width: 130px; float: left; margin-left: 10px; }
.rcname4{ width: 110px; float: left; margin-left: 10px; }
.rcname5{ width: 130px; float: left; margin-left: 10px; }
.rcname6{ width: 130px; float: left; margin-left: 10px; }
.rcname7{ width: 130px; float: left; margin-left: 10px; }

#customer_specific input { width: 100%; }

#customer_specific tr { margin-top: 10px; float: left; }

#customer_specific{display: none;}

#customer_specific{ margin-bottom: 30px; }



#role_specific input { width: 100%; }

#role_specific tr { margin-top: 10px; float: left; }

#role_specific{display: none;}

#role_specific{ margin-bottom: 30px; }

.af_price_div { width: 96%; float: left; padding: 10px; overflow-x: scroll; }

.add_rule_bt_div{ width: 100%; float: right; text-align: right; margin-top: 20px; }

.button-danger{ background-color:
#d97070 !important;
color:
#fff !important;
border: none !important; }

.csp_input { width: 100% !important; }

.afrbp_div{ width: 95%;float: left;padding: 10px;border: solid 2px #0768ad;margin: 10px;}

.afrpb_head{ font-weight: bold; text-align: center !important;}

.afrpb_head_first{ font-weight: bold; text-align: left;}

.afrbp_num_field { width: 60% !important; height: 34px !important; margin: 0 auto !important; float: none !important; display: block;}

.afrbp_inp_field { width: 60% !important; height: 34px !important; margin: 0 auto !important; float: none !important; display: block;}

.afrbp_cat_div{ width: 91%;float: left;padding: 10px;border: solid 2px #0768ad;margin-bottom: 10px; }

.afrbp_cat_divtab{ width: 100%;float: left;padding: 10px;border: solid 2px #0768ad;margin-bottom: 10px; }

.cdiv table tr td { border: solid 1px !important; text-align: center !important; padding: 10px !important; vertical-align: middle !important; }
.cdiv table { padding: 0 !important; border-top: 0 !important; }

.rule_input{ width: 50%; }

.postbox{ position: inherit !important; float: left; width: 100%; }

.csp_msg{ width: 100%; font-size: 11px; margin: 0; margin-top: 5px; }

.hide_price_divs{ width: 100%; float: left; padding-top: 30px; }

.hide_div{ width: 100%; float: left; margin-top: 50px; }

.hide_div label { width: 25%; float: left; font-weight: bold; font-size: 14px; }

#hide_div, #userroles, #hp_price, .hp_cart{ display: none; }

.hide_price_divs .select2-container{ width: 100% !important; }

.csp_hide_field{ width: 70%; float: left; }

.csp_hide_field p{ width: 100%; float: left; }

.csp_hp_input_field { width: 100%; float: left; padding: 10px !important; }

#afrole_save_hide_price{ margin-top: 50px; }

.rep_price{ width: 16px !important; }

.rep_price_td{ text-align: center !important; }

.tooltip {
  position: relative;
  display: inline-block;
  background: #000;
color: #fff;
padding: 0px 6px;
border-radius: 23px;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 12px;
  position: absolute;
  z-index: 1;
  bottom: 150%;
  left: 50%;
  margin-left: -60px;
  font-size: 11px;
  font-weight: normal;
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: black transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
}




