<?php
/**
 * Plugin Name: Backorders Updater
 * Description: A plugin to update `_backorders_us` and `_backorders_eu` meta keys every hour.
 * Version: 1.0.0
 * Author: Y.dev
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Function to update the `_backorders_us` and `_backorders_eu` meta keys.
 */
function update_backorders_meta_keys() {
    global $wpdb;
    $queries = [
        "UPDATE wp_postmeta SET meta_value = 'yes' WHERE meta_key = '_backorders_us'",
        "UPDATE wp_postmeta SET meta_value = 'yes' WHERE meta_key = '_backorders_eu'"
    ];

    foreach ($queries as $query) {
        $wpdb->query($query);
    }
}

function backorders_schedule_event() {
    if (!wp_next_scheduled('backorders_update_event')) {
        wp_schedule_event(time(), 'hourly', 'backorders_update_event');
    }
}
add_action('wp', 'backorders_schedule_event');

add_action('backorders_update_event', 'update_backorders_meta_keys');

function backorders_unschedule_event() {
    $timestamp = wp_next_scheduled('backorders_update_event');
    if ($timestamp) {
        wp_unschedule_event($timestamp, 'backorders_update_event');
    }
}
register_deactivation_hook(__FILE__, 'backorders_unschedule_event');
