<?php

/**
 * Plugin Name: Contract Pricing
 * Description: A plugin to manage special discount pricing for specified customers.
 * Version: 1.0
 * Author: Y.Dev
 */

 require_once plugin_dir_path(__FILE__) . 'contract-pricing-usd.php';
 require_once plugin_dir_path(__FILE__) . 'contract-pricing-eur.php';
 require_once plugin_dir_path(__FILE__) . 'contract-pricing-gbp.php';
// Enqueue required scripts and styles


// Create Admin Menu
function csp_create_admin_menu()
{
    add_menu_page(
        'Contract Pricing',
        'Contract Pricing',
        'manage_options',
        'contract-pricing',
        'csp_usd_page',                    // Function to display content
        'dashicons-upload',                        // Menu icon
        56
    );
}

function add_sub_pricing_menu(){
    add_submenu_page(
        'contract-pricing',
        'Contract Pricing USD',              // Page title
        'USD',              // Menu title
        'manage_options',                          // Capability
        'contract-pricing-usd',                         // Menu slug
        'csp_usd_page',                    // Function to display content
        56                                         // Position
    );
    add_submenu_page(
        'contract-pricing',
        'Contract Pricing EUR',              // Page title
        'EUR',              // Menu title
        'manage_options',                          // Capability
        'contract-pricing-eur',                         // Menu slug
        'csp_eur_page',                    // Function to display content
        56                                         // Position
    );
    add_submenu_page(
        'contract-pricing',
        'Contract Pricing GBP',              // Page title
        'GBP',              // Menu title
        'manage_options',                          // Capability
        'contract-pricing-gbp',                         // Menu slug
        'csp_gbp_page',                    // Function to display content
        56                                         // Position
    );
    remove_submenu_page('contract-pricing', 'contract-pricing');
}
add_action('admin_menu', 'csp_create_admin_menu');
add_action('admin_menu', 'add_sub_pricing_menu');