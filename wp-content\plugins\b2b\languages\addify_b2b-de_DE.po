msgid ""
msgstr ""
"Project-Id-Version: WooCommerce B2B Plugin\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-26 06:36+0000\n"
"PO-Revision-Date: 2020-08-27 11:44+0000\n"
"Last-Translator: \n"
"Language-Team: German\n"
"Language: de_DE\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.4.2; wp-5.5"

#: addify_b2b.php:299
msgid "af-product-visibility"
msgstr "af-Produkt-Sichtbarkeit"

#: addify_b2b.php:300 class_afb2b_admin.php:128
msgid "Products Visibility"
msgstr "Produkte Sichtbarkeit"

#: addify_b2b.php:384 addify_b2b.php:397 class_afb2b_admin.php:70
#: class_afb2b_admin.php:70
msgid "Registration Fields"
msgstr "Felder für die Registrierung"

#: addify_b2b.php:385
msgid "Registration Field"
msgstr "Registrierungsfeld"

#: addify_b2b.php:386 addify_b2b.php:387
msgid "Add New Field"
msgstr "Neues Feld hinzufügen"

#: addify_b2b.php:388
msgid "Edit Registration Field"
msgstr "Registrierungsfeld bearbeiten"

#: addify_b2b.php:389
msgid "New Registration Field"
msgstr "Neues Registrierungsfeld"

#: addify_b2b.php:390
msgid "View Registration Field"
msgstr "Registrierungsfeld anzeigen"

#: addify_b2b.php:391
msgid "Search Registration Field"
msgstr "Suche Registrierungsfeld"

#: addify_b2b.php:393
msgid "No registration field found"
msgstr "Kein Registrierungsfeld gefunden"

#: addify_b2b.php:394
msgid "No registration field found in trash"
msgstr "Kein Registrierungsfeld im Papierkorb gefunden"

#: addify_b2b.php:396
msgid "All Fields"
msgstr "Alle Felder"

#: addify_b2b.php:422
msgid "Request for Quote Rules"
msgstr ""
"Angebotsanfrage\n"

#: addify_b2b.php:423
msgid "Request for QuotevRule"
msgstr ""
"Angebotsanfrage\n"

#: addify_b2b.php:424 addify_b2b.php:425 addify_b2b.php:478
msgid "Add New Rule"
msgstr "Neue Regel hinzufügen"

#: addify_b2b.php:426 addify_b2b.php:480
msgid "Edit Rule"
msgstr "Regel bearbeiten"

#: addify_b2b.php:427 addify_b2b.php:481
msgid "New Rule"
msgstr "Neue Regel"

#: addify_b2b.php:428 addify_b2b.php:482
msgid "View Rule"
msgstr "Regel anzeigen"

#: addify_b2b.php:429 addify_b2b.php:483
msgid "Search Rule"
msgstr "Suchregel"

#: addify_b2b.php:431 addify_b2b.php:485
msgid "No rule found"
msgstr "Keine Regel gefunden"

#: addify_b2b.php:432 addify_b2b.php:486
msgid "No rule found in trash"
msgstr "Keine Regel im Papierkorb gefunden"

#: addify_b2b.php:434 addify_b2b.php:488
msgid "All Rules"
msgstr "Alle Regeln"

#: addify_b2b.php:435 additional_classes/class_afb2b_rfq_front.php:2433
msgid "Request for Quote"
msgstr ""
"Anfrage für ein Angebot\n"

#: addify_b2b.php:464
msgid "All Quotes"
msgstr "Alle Zitate"

#: addify_b2b.php:476 addify_b2b.php:477
msgid "Role Based Pricing Rules"
msgstr "Rollenbasierte Preisgestaltungsregeln"

#: addify_b2b.php:479 includes/csp_product_level_variable_product.php:140
#: includes/csp_product_level.php:138 includes/csp_rule_level.php:395
msgid "Add Rule"
msgstr "Regel hinzufügen"

#: addify_b2b.php:489 class_afb2b_admin.php:76 class_afb2b_admin.php:76
#: class_afb2b_admin.php:134
msgid "Role Based Pricing"
msgstr ""
"Rollenbasierte Preisgestaltung\n"

#. page title
#: class_afb2b_admin.php:59 class_afb2b_admin.php:60
msgid "B2B"
msgstr "B2B"

#: class_afb2b_admin.php:68
msgid "B2B Settings"
msgstr ""
"B2B-Einstellungen\n"

#: class_afb2b_admin.php:68 class_afb2b_admin.php:123
msgid "Settings"
msgstr "Einstellungen"

#: class_afb2b_admin.php:72
msgid "Request a Quote Rules"
msgstr ""
"Fordern Sie Angebotsregeln an\n"

#: class_afb2b_admin.php:74 class_afb2b_admin.php:215
msgid "All Submitted Quotes"
msgstr "Alle eingereichten Zitate"

#: class_afb2b_admin.php:130
msgid "Request a Quote"
msgstr "Angebot anfordern"

#: class_afb2b_admin.php:132
msgid "B2B Registration"
msgstr ""
"B2B-Registrierung\n"

#: class_afb2b_admin.php:135
msgid "Tax-Exempt"
msgstr ""
"Steuerbefreit\n"

#: class_afb2b_admin.php:136
msgid "Shipping"
msgstr ""
"Versand\n"

#: class_afb2b_admin.php:137
msgid "Payments"
msgstr ""
"Zahlungen\n"

#: class_afb2b_admin.php:147
msgid "Global Settings"
msgstr ""
"Globale Einstellungen\n"

#. ID used to identify the field throughout the theme
#: class_afb2b_admin.php:151 includes/afb2b_product_visibility_settings.php:132
msgid "Visibility By User Roles"
msgstr "Sichtbarkeit nach Benutzerrollen"

#: class_afb2b_admin.php:155 class_afb2b_admin.php:190
#: class_afb2b_admin.php:260 class_afb2b_admin.php:337
#: includes/afb2b_product_visibility_settings.php:1006
msgid "General Settings"
msgstr "Allgemeine Einstellungen"

#: class_afb2b_admin.php:195
msgid "Fields Settings"
msgstr "Einstellungen für Felder"

#: class_afb2b_admin.php:200
msgid "Redirect Settings"
msgstr ""
"Einstellungen umleiten\n"

#: class_afb2b_admin.php:205
msgid "Captcha Settings"
msgstr "Captcha-Einstellungen"

#: class_afb2b_admin.php:210
msgid "All Quote Rules"
msgstr ""
"Alle Angebotsregeln\n"

#: class_afb2b_admin.php:265
msgid "Enable Default Fields"
msgstr "Standardfelder aktivieren"

#: class_afb2b_admin.php:270
msgid "User Role Settings"
msgstr "Benutzerrollen-Einstellungen"

#: class_afb2b_admin.php:275
msgid "Approve New User Settings"
msgstr "Neue Benutzereinstellungen genehmigen"

#: class_afb2b_admin.php:280
msgid "Email Settings"
msgstr "E-Mail-Einstellungen"

#: class_afb2b_admin.php:285
msgid "All Registration Fields"
msgstr ""
"Alle Registrierungsfelder\n"

#: class_afb2b_admin.php:342
msgid "All Role Based Pricing Rules"
msgstr ""
"Alle rollenbasierten Preisregeln\n"

#: class_afb2b_admin.php:385
msgid "General"
msgstr ""
"Allgemeines\n"

#: class_afb2b_admin.php:390
msgid "Customers and Roles"
msgstr ""
"Kunden und Rollen\n"

#: class_afb2b_admin.php:395
msgid "Exemption Request"
msgstr ""
"Ausnahmeantrag\n"

#: class_afb2b_admin.php:400
msgid "Email & Notification"
msgstr ""
"Email Benachrichtigung\n"

#: class_afb2b_admin.php:405
msgid "Guest Users"
msgstr ""
"Gastbenutzer\n"

#: includes/addify-afrfq-edit-form.php:19
msgid "View Quote"
msgstr "Zitat ansehen"

#: includes/addify-afrfq-edit-form.php:25
msgid "Quote #:"
msgstr "Angebot #:"

#: includes/addify-afrfq-edit-form.php:45
#: includes/addify-afrfq-edit-form.php:66
#: includes/addify-afrfq-edit-form.php:90
#: includes/addify-afrfq-edit-form.php:114
#: includes/addify-afrfq-edit-form.php:138
#: includes/addify-afrfq-edit-form.php:161
#: includes/addify-afrfq-edit-form.php:184
#: includes/addify-afrfq-edit-form.php:208
#: includes/addify-afrfq-edit-form.php:232
#: additional_classes/class_afb2b_rfq_front.php:2053
#: additional_classes/class_afb2b_rfq_front.php:2071
#: additional_classes/class_afb2b_rfq_front.php:2089
#: additional_classes/class_afb2b_rfq_front.php:2107
#: additional_classes/class_afb2b_rfq_front.php:2125
#: additional_classes/class_afb2b_rfq_front.php:2143
#: additional_classes/class_afb2b_rfq_front.php:2161
#: additional_classes/class_afb2b_rfq_front.php:2179
#: additional_classes/class_afb2b_rfq_front.php:2197
msgid ":"
msgstr ":"

#: includes/addify-afrfq-edit-form.php:139
#: additional_classes/class_afb2b_rfq_front.php:2128
msgid "Click to View"
msgstr "Zur Ansicht anklicken"

#: includes/addify-afrfq-edit-form.php:247
#: includes/addify_quote_request_page.php:74
#: additional_classes/class_afb2b_rfq_front.php:2220
#: additional_classes/class_afb2b_rfq_front.php:2726
msgid "Product"
msgstr "Produkt"

#: includes/addify-afrfq-edit-form.php:248
#: includes/addify_quote_request_page.php:75
msgid "Product SKU"
msgstr "Produkt SKU"

#: includes/addify-afrfq-edit-form.php:249
#: includes/addify_quote_request_page.php:76
#: additional_classes/class_afb2b_rfq_front.php:2222
#: additional_classes/class_afb2b_rfq_front.php:2728
msgid "Quantity"
msgstr "Anzahl"

#: includes/csp_product_level_variable_product.php:3
#: includes/csp_product_level.php:4 includes/csp_product_level.php:150
msgid "Important Notes:"
msgstr "Wichtige Hinweise:"

#: includes/csp_product_level_variable_product.php:5
#: includes/csp_product_level.php:6 includes/csp_product_level.php:152
msgid "Pricing Priority:"
msgstr "Priorität bei der Preisgestaltung:"

#: includes/csp_product_level_variable_product.php:7
#: includes/csp_product_level.php:8 includes/csp_product_level.php:154
msgid "Price Specific to a Customer"
msgstr "Kundenspezifischer Preis"

#: includes/csp_product_level_variable_product.php:8
#: includes/csp_product_level.php:9 includes/csp_product_level.php:155
msgid "Price Specific to a Role"
msgstr "Rollenspezifischer Preis"

#: includes/csp_product_level_variable_product.php:9
#: includes/csp_product_level.php:10 includes/csp_product_level.php:156
msgid "Regular Product Price"
msgstr "Regulärer Produktpreis"

#: includes/csp_product_level_variable_product.php:17
#: includes/csp_product_level.php:16 includes/csp_rule_level.php:304
#: additional_classes/class_afb2b_role_based_pricing_admin.php:63
msgid "Role Based Pricing(By Customers)"
msgstr ""
"Rollenbasierte Rollenbasierte Preisgestaltung (von Kunden)\n"

#: includes/csp_product_level_variable_product.php:18
#: includes/csp_product_level.php:17 includes/csp_rule_level.php:305
msgid ""
"If more than one rule is applied on same customer then rule that is added "
"last will be applied."
msgstr ""
"Wenn mehr als eine Regel auf denselben Kunden angewendet wird, wird die "
"zuletzt hinzugefügte Regel angewendet."

#: includes/csp_product_level_variable_product.php:23
#: includes/csp_product_level.php:22 includes/csp_rule_level.php:310
msgid "Customer"
msgstr "Kunde"

#: includes/csp_product_level_variable_product.php:24
#: includes/csp_product_level_variable_product.php:155
#: includes/csp_product_level.php:23 includes/csp_product_level.php:167
#: includes/csp_rule_level.php:311 includes/csp_rule_level.php:414
msgid "Adjustment Type"
msgstr "Einstellungstyp"

#: includes/csp_product_level_variable_product.php:25
#: includes/csp_product_level_variable_product.php:156
#: includes/csp_product_level.php:24 includes/csp_product_level.php:168
#: includes/csp_rule_level.php:312 includes/csp_rule_level.php:415
msgid "Value"
msgstr "Wert"

#: includes/csp_product_level_variable_product.php:26
#: includes/csp_product_level_variable_product.php:157
#: includes/csp_product_level.php:25 includes/csp_product_level.php:169
#: includes/csp_rule_level.php:313 includes/csp_rule_level.php:416
msgid "Min Qty"
msgstr "Min. Menge"

#: includes/csp_product_level_variable_product.php:27
#: includes/csp_product_level_variable_product.php:158
#: includes/csp_product_level.php:26 includes/csp_product_level.php:170
#: includes/csp_rule_level.php:314 includes/csp_rule_level.php:417
msgid "Max Qty"
msgstr "Maximale Menge"

#: includes/csp_product_level_variable_product.php:28
#: includes/csp_product_level.php:27 includes/csp_product_level.php:171
#: includes/csp_rule_level.php:418
msgid "Replace Orignal Price?"
msgstr ""
"Originalpreis ersetzen?\n"

#: includes/csp_product_level_variable_product.php:30
#: includes/csp_product_level_variable_product.php:161
#: includes/csp_product_level.php:29 includes/csp_product_level.php:173
#: includes/csp_rule_level.php:317 includes/csp_rule_level.php:420
msgid ""
"This will only work for Fixed Price, Fixed Decrease and Percentage Decrease."
msgstr ""
"Dies funktioniert nur für Festpreis, Festpreisreduzierung und prozentuale "
"Reduzierung."

#: includes/csp_product_level_variable_product.php:33
#: includes/csp_product_level.php:32 includes/csp_rule_level.php:320
msgid "Remove"
msgstr "entfernen"

#: includes/csp_product_level_variable_product.php:79
#: includes/csp_product_level_variable_product.php:189
#: includes/csp_product_level_variable_product.php:223
#: includes/csp_product_level_variable_product.php:272
#: includes/csp_product_level.php:79 includes/csp_product_level.php:201
#: includes/csp_product_level.php:234 includes/csp_product_level.php:277
#: includes/csp_rule_level.php:358 includes/csp_rule_level.php:455
#: includes/csp_rule_level.php:489 includes/csp_rule_level.php:535
msgid "Fixed Price"
msgstr "Fester Preis"

#: includes/csp_product_level_variable_product.php:80
#: includes/csp_product_level_variable_product.php:190
#: includes/csp_product_level_variable_product.php:224
#: includes/csp_product_level_variable_product.php:273
#: includes/csp_product_level.php:80 includes/csp_product_level.php:202
#: includes/csp_product_level.php:235 includes/csp_product_level.php:278
#: includes/csp_rule_level.php:359 includes/csp_rule_level.php:456
#: includes/csp_rule_level.php:490 includes/csp_rule_level.php:536
msgid "Fixed Increase"
msgstr "Feste Erhöhung"

#: includes/csp_product_level_variable_product.php:81
#: includes/csp_product_level_variable_product.php:191
#: includes/csp_product_level_variable_product.php:225
#: includes/csp_product_level_variable_product.php:274
#: includes/csp_product_level.php:81 includes/csp_product_level.php:203
#: includes/csp_product_level.php:236 includes/csp_product_level.php:279
#: includes/csp_rule_level.php:360 includes/csp_rule_level.php:457
#: includes/csp_rule_level.php:491 includes/csp_rule_level.php:537
msgid "Fixed Decrease"
msgstr ""
"Feste Abnahme\n"

#: includes/csp_product_level_variable_product.php:82
#: includes/csp_product_level_variable_product.php:192
#: includes/csp_product_level_variable_product.php:226
#: includes/csp_product_level_variable_product.php:275
#: includes/csp_product_level.php:82 includes/csp_product_level.php:204
#: includes/csp_product_level.php:237 includes/csp_product_level.php:280
#: includes/csp_rule_level.php:361 includes/csp_rule_level.php:458
#: includes/csp_rule_level.php:492 includes/csp_rule_level.php:538
msgid "Percentage Decrease"
msgstr "Prozentuale Abnahme"

#: includes/csp_product_level_variable_product.php:83
#: includes/csp_product_level_variable_product.php:193
#: includes/csp_product_level_variable_product.php:227
#: includes/csp_product_level_variable_product.php:276
#: includes/csp_product_level.php:83 includes/csp_product_level.php:205
#: includes/csp_product_level.php:238 includes/csp_product_level.php:281
#: includes/csp_rule_level.php:362 includes/csp_rule_level.php:459
#: includes/csp_rule_level.php:493 includes/csp_rule_level.php:539
msgid "Percentage Increase"
msgstr "Prozentuale Erhöhung"

#: includes/csp_product_level_variable_product.php:116
#: includes/csp_product_level_variable_product.php:309
#: includes/csp_product_level.php:114 includes/csp_product_level.php:314
#: includes/csp_rule_level.php:572
msgid "X"
msgstr "X."

#: includes/csp_product_level_variable_product.php:148
#: includes/csp_product_level.php:162 includes/csp_rule_level.php:407
#: additional_classes/class_afb2b_role_based_pricing_admin.php:70
msgid "Role Based Pricing(By User Roles)"
msgstr ""
"Rollenbasierte Preisgestaltung (nach Benutzerrollen)\n"

#: includes/csp_product_level_variable_product.php:154
#: includes/csp_product_level.php:166 includes/csp_rule_level.php:413
msgid "User Role"
msgstr "Benutzer-Rolle"

#: includes/csp_product_level_variable_product.php:159
#: includes/csp_rule_level.php:315
msgid "Replace Original Price?"
msgstr ""
"Originalpreis ersetzen?\n"

#: includes/csp_product_level_variable_product.php:188
#: includes/csp_product_level_variable_product.php:222
#: includes/csp_product_level.php:200 includes/csp_product_level.php:233
#: includes/csp_rule_level.php:454 includes/csp_rule_level.php:488
msgid "---Select Adjustment Type---"
msgstr "---Einstelltyp wählen---"

#: includes/csp_product_level_variable_product.php:219
#: includes/csp_product_level.php:230 includes/csp_rule_level.php:485
msgid "Non LoggedIn/Guest"
msgstr "Nicht eingeloggter/Gast"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:12
msgid "Additional Fields Section Title"
msgstr "Titel des Abschnitts Zusätzliche Felder"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:17
msgid ""
"This is the title for the section where additional fields are displayed on "
"front end registration form."
msgstr ""
"Dies ist der Titel für den Abschnitt, in dem zusätzliche Felder auf dem "
"Frontend-Registrierungsformular angezeigt werden."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:34
#: includes/afb2b_rfq_settings.php:280
msgid "Site Key"
msgstr "Standort-Schlüssel"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:39
#: includes/afb2b_rfq_settings.php:285
msgid ""
"This is Google reCaptcha site key, you can get this from Google. Without "
"this key Google reCaptcha will not work."
msgstr ""
"Dies ist der geheime Schlüssel für Google reCaptcha, den Sie von Google "
"erhalten können. Ohne diesen Schlüssel wird Google reCaptcha nicht "
"funktionieren."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:49
#: includes/afb2b_rfq_settings.php:295
msgid "Secret Key"
msgstr "Geheimer Schlüssel"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:54
#: includes/afb2b_rfq_settings.php:300
msgid ""
"This is Google reCaptcha secret key, you can get this from Google. Without "
"this key Google reCaptcha will not work."
msgstr ""
"Dies ist der geheime Schlüssel von Google reCaptcha, den Sie von Google "
"erhalten können. Ohne diesen Schlüssel wird Google reCaptcha nicht "
"funktionieren."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:73
msgid "Enable User Role Selection"
msgstr "Benutzerrollenauswahl aktivieren"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:78
msgid ""
"Enable/Disable User Role selection on registration page. If this is enable "
"then a user role dropdown will be shown on registration page."
msgstr ""
"Aktivieren / Deaktivieren der Auswahl der Benutzerrolle auf der "
"Registrierungsseite. Wenn diese Option aktiviert ist, wird auf der "
"Registrierungsseite ein Dropdown-Menü für Benutzerrollen angezeigt.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:88
msgid "User Role Field Label"
msgstr "Benutzerrollen-Feldbeschriftung"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:93
msgid "Field label for user role selection select box."
msgstr "Field label for user role selection select box."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:103
#: includes/tax-exempt/exempt_customers_roles.php:27
#: includes/tax-exempt/exempt_request.php:12
msgid "Select User Roles"
msgstr "Benutzerrollen auswählen"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:108
msgid ""
"Select which user roles you want to show in dropdown on registration page. "
"Note: Administrator role is not available for show in dropdown."
msgstr ""
"Wählen Sie auf der Registrierungsseite aus, welche Benutzerrollen in der "
"Dropdown-Liste angezeigt werden sollen. Hinweis: Die Administratorrolle kann "
"nicht in der Dropdown-Liste angezeigt werden.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:127
msgid "Enable Approve New User"
msgstr "Neuen Benutzer freigeben aktivieren"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:132
msgid ""
"Enable/Disable Approve new user. When this option is enabled all new "
"registered users will be set to Pending until admin approves"
msgstr ""
"Aktivieren/Deaktivieren Neuer Benutzer genehmigen. Wenn diese Option "
"aktiviert ist, werden alle neu registrierten Benutzer auf Ausstehend gesetzt,"
" bis der Administrator sie genehmigt."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:142
msgid "Enable Approve New User at Checkout Page"
msgstr ""
"Aktivieren Sie \"Neuen Benutzer an der Checkout-Seite genehmigen\"\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:147
msgid ""
"Enable/Disable Approve new user at the Checkout page. If you enable it, the "
"order of the customer with registration will be placed and pending status is "
"assigned to the user. Once the user logout from the site, he will not able "
"to log in again until the administrator approves the user. If you disable it,"
" the user will be approved automatically when registered from the checkout "
"page."
msgstr ""
"Aktivieren / Deaktivieren Genehmigen Sie neuen Benutzer auf der Checkout-"
"Seite. Wenn Sie es aktivieren, wird die Bestellung des Kunden mit "
"Registrierung aufgegeben und dem Benutzer der Status Ausstehend zugewiesen. "
"Sobald sich der Benutzer von der Site abgemeldet hat, kann er sich erst "
"wieder anmelden, wenn der Administrator den Benutzer genehmigt hat. Wenn Sie "
"es deaktivieren, wird der Benutzer automatisch genehmigt, wenn er auf der "
"Checkout-Seite registriert wird.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:157
msgid "Exclude User Roles"
msgstr "Benutzerrollen ausschließen"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:162
msgid ""
"Select which user roles users you want to exclude from manual approval. "
"These user roles users will be automatically approved."
msgstr ""
"Wählen Sie die Benutzerrollen aus, die Sie von der manuellen Genehmigung "
"ausschließen möchten. Diese Benutzerrollen werden automatisch genehmigt."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:179
msgid "Message for Users when Account is Created"
msgstr "Nachricht für Benutzer, wenn ein Konto angelegt wird"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:184
msgid ""
"First message that will be displayed to user when he/she completes the "
"registration process, this message will be displayed only when manual "
"approval is required. "
msgstr ""
"Erste Meldung, die dem Benutzer angezeigt wird, wenn er den "
"Registrierungsvorgang abgeschlossen hat. Diese Meldung wird nur angezeigt, "
"wenn eine manuelle Genehmigung erforderlich ist.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:194
msgid "Message for Users when Account is pending for approval"
msgstr "Nachricht für Benutzer, wenn das Konto zur Genehmigung ansteht"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:199
msgid ""
"This will be displayed when user will attempt to login after registration "
"and his/her account is still pending for admin approval. "
msgstr ""
"Dies wird angezeigt, wenn der Benutzer nach der Registrierung versucht, sich "
"anzumelden, und sein Konto noch zur Genehmigung durch den Administrator "
"aussteht.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:209
msgid "Message for Users when Account is disapproved"
msgstr "Nachricht für Benutzer, wenn das Konto abgelehnt wird"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:214
msgid "Message for Users when Account is Disapproved By Admin."
msgstr ""
"Nachricht für Benutzer, wenn das Konto von der Verwaltung nicht genehmigt "
"wird."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:235
msgid "Admin Email Address"
msgstr "E-Mail-Adresse der Verwaltung"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:240
msgid ""
"This email address will be used for getting new user email notification for "
"admin, if this is empty then default WordPress admin email address will be "
"used."
msgstr ""
"Diese E-Mail-Adresse wird verwendet, um eine E-Mail-Benachrichtigung für "
"neue Benutzer an den Admin zu erhalten. Wenn diese leer ist, wird die "
"defualt wordpress Admin-E-Mail-Adresse verwendet."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:251
msgid "Enable admin email notification"
msgstr ""
"Aktivieren Sie die E-Mail-Benachrichtigung des Administrators\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:256
msgid "Enable or Disable new user notification to admin from this module. "
msgstr ""
"Aktivieren oder Deaktivieren der Benachrichtigung neuer Benutzer an den "
"Administrator über dieses Modul.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:267
msgid "Admin Email Subject"
msgstr "E-Mail-Betreff für Administratoren"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:272
msgid ""
"This email subject is used when new user notification is sent to admin. "
msgstr ""
"Dieser E-Mail-Betreff wird verwendet, wenn eine Benachrichtigung über neue "
"Benutzer an den Administrator gesendet wird."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:282
msgid "Admin Email Text"
msgstr "E-Mail-Text für Administratoren"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:287
msgid ""
"This email text will be used when new user notification is sent to admin. If "
"Approve new user is active then you can write text about new user approval."
msgstr ""
"Dieser E-Mail-Text wird verwendet, wenn eine Benachrichtigung für neue "
"Benutzer an den Admin geschickt wird. Wenn Neuer Benutzer genehmigen aktiv "
"ist, können Sie einen Text über die Genehmigung des neuen Benutzers "
"schreiben."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:299
msgid "Enable welcome email notification"
msgstr ""
"Aktivieren Sie die Begrüßungs-E-Mail-Benachrichtigung\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:304
msgid "Enable or Disable welcome email notification from this module. "
msgstr ""
"Aktivieren oder Deaktivieren der Begrüßungs-E-Mail-Benachrichtigung in "
"diesem Modul.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:315
msgid "Welcome/Pending Email Subject"
msgstr "Begrüßung/Ausstehender E-Mail-Betreff"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:320
msgid ""
"This is the email subject; this subject is used when the email is sent to "
"the user on account creation to include fields data and manual approval "
"message."
msgstr ""
"Dies ist der E-Mail-Betreff; dieser Betreff wird verwendet, wenn die E-Mail "
"bei der Kontoerstellung an den Benutzer gesendet wird, um die Felddaten und "
"die manuelle Genehmigungsnachricht einzuschließen."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:330
msgid "Welcome/Pending Email Body Text"
msgstr "Begrüßung/Ausstehender E-Mail-Text"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:335
msgid ""
"This is the email body; when a new customer registers this email be "
"automatically sent and the custom fields will be included in that email. "
"This body text will be included along with the default fields data."
msgstr ""
"Dies ist der E-Mail-Text; wenn sich ein neuer Kunde registriert, wird diese "
"E-Mail automatisch versandt, und die benutzerdefinierten Felder werden in "
"diese E-Mail aufgenommen. Dieser Textkörper wird zusammen mit den Daten der "
"Standardfelder eingefügt."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:346
msgid "Approved Email Subject"
msgstr "Genehmigter E-Mail-Betreff"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:351
msgid ""
"This is the approved email subject, this subject is when used when account "
"is approved by administrator.  "
msgstr ""
"Dies ist der genehmigte E-Mail-Betreff. Dieser Betreff wird verwendet, wenn "
"das Konto vom Administrator genehmigt wird."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:361
msgid "Approved Email Text"
msgstr "Genehmigter E-Mail-Text"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:366
msgid ""
"This is the approved email message, this message is used when account is "
"approved by administrator. "
msgstr ""
"Dies ist die genehmigte E-Mail-Nachricht, diese Nachricht wird verwendet, "
"wenn das Konto vom Administrator genehmigt wird.\n"
"\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:377
msgid "Disapproved Email Subject"
msgstr ""
"Abgelehnter E-Mail-Betreff\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:382
msgid ""
"This is the disapproved email subject, this subject is used when account is "
"disapproved by administrator."
msgstr ""
"Dies ist der abgelehnte E-Mail-Betreff, dieser Betreff wird verwendet, wenn "
"das Konto vom Administrator abgelehnt wurde."

#. ID used to identify the field throughout the theme
#: includes/afb2b_registration_settings.php:392
msgid "Disapproved Email Text"
msgstr "Nicht genehmigter E-Mail-Text"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_registration_settings.php:397
msgid ""
"This is the disapproved email message, this message is used when account is "
"disapproved by administrator."
msgstr ""
"Dies ist die abgelehnte E-Mail-Nachricht, diese Nachricht wird verwendet, "
"wenn das Konto vom Administrator abgelehnt wurde."

#: includes/afb2b_registration_settings.php:414
msgid "Manage registration module general settings from here."
msgstr ""
"Verwalten Sie die allgemeinen Einstellungen des Registrierungsmoduls von "
"hier aus."

#: includes/afb2b_registration_settings.php:430
msgid "Google reCaptcha Settings"
msgstr "Google reCaptcha-Einstellungen"

#: includes/afb2b_registration_settings.php:454
msgid ""
"Manage user role settings from here. Choose whether you want to show user "
"role dropdown on registration page or not and choose which user roles you "
"want to show in dropdown on registration page."
msgstr ""
"Verwalten Sie die Benutzerrolleneinstellungen von hier aus. Wählen Sie aus, "
"ob die Benutzerrollen-Dropdown-Liste auf der Registrierungsseite angezeigt "
"werden soll oder nicht, und wählen Sie aus, welche Benutzerrollen in der "
"Dropdown-Liste auf der Registrierungsseite angezeigt werden sollen.\n"

#: includes/afb2b_registration_settings.php:521
msgid "Manage Approve new user settings from here."
msgstr "Verwalten Neue Benutzereinstellungen von hier aus genehmigen."

#: includes/afb2b_registration_settings.php:588
msgid "Approve New User Messages Settings"
msgstr "Einstellungen für neue Benutzernachrichten genehmigen"

#: includes/afb2b_registration_settings.php:621
msgid "Manage Email Settings from here."
msgstr ""
"Verwalten Sie die E-Mail-Einstellungen von hier aus.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_product_visibility_settings.php:12
msgid "Enable Global Visibility"
msgstr ""
"Aktivieren Sie die globale Sichtbarkeit\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:17
msgid "Enable or Disable global visibility."
msgstr ""
"Aktivieren oder Deaktivieren der globalen Sichtbarkeit.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_product_visibility_settings.php:27
#: includes/afb2b_product_visibility_settings.php:593
msgid "Show/Hide"
msgstr "Anzeigen/Verbergen"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:32
msgid "Select either you want to show products or hide products."
msgstr "Wählen Sie aus, ob Sie Produkte anzeigen oder ausblenden möchten."

#. ID used to identify the field throughout the theme
#: includes/afb2b_product_visibility_settings.php:43
#: includes/afb2b_product_visibility_settings.php:606
#: includes/csp_rule_level.php:29
msgid "Select Products"
msgstr ""
"Wählen Sie Produkte\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:48
msgid "Select products on which you want to apply."
msgstr "Wählen Sie die Produkte aus, für die Sie sich bewerben möchten."

#. ID used to identify the field throughout the theme
#: includes/afb2b_product_visibility_settings.php:58
#: includes/afb2b_product_visibility_settings.php:637
#: includes/csp_rule_level.php:61
msgid "Select Categories"
msgstr "Kategorien auswählen"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:63
msgid "Select categories on which products on which you want to apply."
msgstr ""
"Wählen Sie die Kategorien aus, für welche Produkte Sie sich bewerben möchten."

#. ID used to identify the field throughout the theme
#: includes/afb2b_product_visibility_settings.php:74
#: includes/afb2b_product_visibility_settings.php:876
msgid "Redirection Mode"
msgstr ""
"Umleitungsmodus\n"

#: includes/afb2b_product_visibility_settings.php:88
#: includes/afb2b_product_visibility_settings.php:470
#: includes/afb2b_product_visibility_settings.php:899
#: includes/afb2b_product_visibility_settings.php:910
msgid "Custom URL"
msgstr "Benutzerdefinierte URL"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:93
msgid ""
"Redirect to this custom URL when user try to access restricted catalog. e.g "
"http://www.example.com"
msgstr ""
"Leiten Sie zu dieser benutzerdefinierten URL um, wenn der Benutzer versucht, "
"auf den eingeschränkten Katalog zuzugreifen. z. B. http://www.example.com\n"

#: includes/afb2b_product_visibility_settings.php:105
#: includes/afb2b_product_visibility_settings.php:471
#: includes/afb2b_product_visibility_settings.php:900
#: includes/afb2b_product_visibility_settings.php:923
msgid "Custom Message"
msgstr "Benutzerdefinierte Nachricht"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:110
msgid ""
"This message will be displayed when user try to access restricted catalog."
msgstr ""
"Diese Meldung wird angezeigt, wenn der Benutzer versucht, auf den Katalog "
"zuzugreifen."

#. ID used to identify the field throughout the theme
#: includes/afb2b_product_visibility_settings.php:155
msgid "Allow Search Engines to Index"
msgstr "Suchmaschinen die Indizierung erlauben"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_product_visibility_settings.php:160
msgid ""
"Allow search engines to crawl and index hidden products, categories and "
"other pages. While using global option when you hide products from guest "
"users they will stay hidden for search engines as well i.e. Google won’t be "
"able to rank those pages in search results. Please check this box if you "
"want Google to crawl and rank hidden pages."
msgstr ""
"Erlauben Sie Suchmaschinen, versteckte Produkte, Kategorien und andere "
"Seiten zu durchsuchen und zu indizieren. Wenn Sie bei Verwendung der "
"globalen Option Produkte vor Gastnutzern ausblenden, bleiben diese auch für "
"Suchmaschinen verborgen, d. h. Google kann diese Seiten nicht in den "
"Suchergebnissen platzieren. Aktivieren Sie dieses Kontrollkästchen, wenn Sie "
"möchten, dass Google versteckte Seiten durchsucht und ein Ranking erstellt."

#: includes/afb2b_product_visibility_settings.php:173
msgid "Global Visibility Settings"
msgstr "Globale Sichtbarkeitseinstellungen"

#: includes/afb2b_product_visibility_settings.php:174
msgid ""
"This will help you to show or hide products for all customers including "
"guests."
msgstr ""
"Auf diese Weise können Sie Produkte für alle Kunden, einschließlich Gäste, "
"ein- oder ausblenden.\n"

#: includes/afb2b_product_visibility_settings.php:175
msgid ""
"Please note that Visibility by User Roles have high priority. If "
"configurations are active for any user role – the global settings won’t work "
"for that specific role."
msgstr ""
"Bitte beachten Sie, dass die Sichtbarkeit nach Benutzerrollen eine hohe "
"Priorität hat. Wenn Konfigurationen für eine Benutzerrolle aktiv sind, "
"funktionieren die globalen Einstellungen für diese bestimmte Rolle nicht.\n"

#: includes/afb2b_product_visibility_settings.php:191
#: includes/afb2b_product_visibility_settings.php:597
msgid "Hide"
msgstr ""
"Ausblenden\n"

#: includes/afb2b_product_visibility_settings.php:192
#: includes/afb2b_product_visibility_settings.php:598
msgid "Show"
msgstr "Show"

#: includes/afb2b_product_visibility_settings.php:522
msgid ""
"Please note that Visibility by User Roles have high priority. If following "
"configurations are active for any user role – the global settings won’t work "
"for that specific role."
msgstr ""
"Bitte beachten Sie, dass die Sichtbarkeit nach Benutzerrollen eine hohe "
"Priorität hat. Wenn die folgenden Konfigurationen für eine Benutzerrolle "
"aktiv sind, funktionieren die globalen Einstellungen für diese bestimmte "
"Rolle nicht.\n"

#: includes/afb2b_product_visibility_settings.php:579
msgid "Enable for this Role"
msgstr ""
"Für diese Rolle aktivieren\n"

#: includes/afb2b_role_based_pricing_settings.php:12
msgid "Min Qty Error Message"
msgstr "Min Qty-Fehlermeldung"

#: includes/afb2b_role_based_pricing_settings.php:28
msgid "Max Qty Error Message"
msgstr "Max Qty Fehlermeldung"

#: includes/afb2b_role_based_pricing_settings.php:45
msgid "Update Cart Error Message"
msgstr "Update-Wagen-Fehlermeldung"

#: includes/afb2b_role_based_pricing_settings.php:62
msgid "Manage module general settings from here."
msgstr "Verwalten Sie von hier aus die allgemeinen Einstellungen des Moduls."

#. ID used to identify the field throughout the theme
#: includes/afreg_def_fields.php:11
msgid "Default Fields"
msgstr ""
"Standardfelder\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afreg_def_fields.php:16
msgid "Enable/Disable Default Fields of WooCommerce."
msgstr ""
"Aktivieren / Deaktivieren der Standardfelder von WooCommerce.\n"

#: includes/afreg_def_fields.php:27
msgid "Default Fields for Registration Settings"
msgstr ""
"Standardfelder für Registrierungseinstellungen\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:12
msgid "Quote Basket Placement"
msgstr "Angebot Korbplatzierung"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:17
msgid ""
"Select Menu where you want to show Mini Quote Basket. If there is no menu "
"then you have to create menu in WordPress menus otherwise mini quote basket "
"will not show."
msgstr ""
"Wählen Sie das Menü, in dem Sie den Mini-Angebotskorb anzeigen möchten. Wenn "
"es kein Menü gibt, müssen Sie das Menü in WordPress-Menüs erstellen, sonst "
"wird der Mini-Angebotskorb nicht angezeigt."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:27
msgid "Quote Basket Style"
msgstr ""
"Zitat Korb-Stil\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:32
msgid "Select the design of Quote Basket"
msgstr ""
"Wählen Sie das Design des Angebotskorbs\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:42
msgid "Enable for Guest"
msgstr "Aktivieren für Gast"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:47
msgid "Enable or Disable quote for guest users."
msgstr "Angebot für Gastbenutzer aktivieren oder deaktivieren."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:57
msgid "Enable Ajax add to Quote (Shop Page)"
msgstr "Ajax zum Zitat hinzufügen aktivieren (Shop-Seite)"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:62
msgid "Enable or Disable Ajax add to quote on shop page."
msgstr "Aktivieren oder deaktivieren Sie Ajax add to quote auf der Shop-Seite."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:72
msgid "Enable Ajax add to Quote (Product Page)"
msgstr "Aktivieren Sie Ajax add to quote (Produktseite)"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:77
msgid "Enable or Disable Ajax add to quote on product page."
msgstr ""
"Aktivieren oder deaktivieren Sie Ajax add to quote auf der Produktseite."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:87
msgid "Success Message"
msgstr "Erfolgsmeldung"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:92
msgid ""
"This message will appear on quote submission page, when user submit quote."
msgstr ""
"Diese Meldung erscheint auf der Seite zur Angebotsabgabe, wenn der Benutzer "
"ein Angebot abgibt."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:102
msgid "Email Subject"
msgstr "E-Mail-Betreff"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:107
msgid ""
"This subject will be used when email is sent to user when quote is submitted."
msgstr ""
"Dieser Betreff wird verwendet, wenn eine E-Mail an den Nutzer gesendet wird, "
"wenn ein Angebot abgegeben wird."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:117
msgid "Email Response Text"
msgstr "E-Mail-Antwort Text"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:122
msgid ""
"This text will be used when email is sent to user when quote is submitted."
msgstr ""
"Dieser Text wird verwendet, wenn bei der Angebotsabgabe eine E-Mail an den "
"Benutzer gesendet wird."

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:132
msgid "Send Email to Customer"
msgstr ""
"E-Mail an Kunden senden\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:137
msgid ""
"Enable this if you want to send email to customer after submitting a Quote."
msgstr ""
"Aktivieren Sie diese Option, wenn Sie nach dem Absenden eines Angebots eine "
"E-Mail an den Kunden senden möchten.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:148
msgid "Include Copy of Quote in Email"
msgstr ""
"Kopie des Angebots in E-Mail einfügen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:153
msgid ""
"Enable this if you want to include a copy of quote details in the email sent "
"to customer. The quote details will be embedded along the with the above "
"email body text."
msgstr ""
"Aktivieren Sie diese Option, wenn Sie eine Kopie der Angebotsdetails in die "
"an den Kunden gesendete E-Mail aufnehmen möchten. Die Angebotsdetails werden "
"zusammen mit dem obigen E-Mail-Text eingebettet.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:163
#: includes/tax-exempt/email_notification.php:12
#: includes/tax-exempt/email_notification.php:27
#: includes/tax-exempt/email_notification.php:43
msgid "Admin/Shop Manager Email"
msgstr "Admin/Shop-Manager E-Mail"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:168
msgid ""
"All admin emails that are related to our module will be sent to this email "
"address. If this email is empty then default admin email address is used."
msgstr ""
"Alle administrativen E-Mails, die sich auf unser Modul beziehen, werden an "
"diese E-Mail-Adresse gesendet. Wenn diese E-Mail-Adresse leer ist, wird die "
"Standard-E-Mail-Adresse des Administrators verwendet. "

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:189
msgid "Request a Quote Form Fields"
msgstr ""
"Fordern Sie ein Angebotsformular an\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:211
msgid "Redirect to Quote Page"
msgstr ""
"Zur Angebotsseite umleiten\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:216
msgid "Redirect to Quote page after a product added to Quote successfully."
msgstr ""
"Weiterleiten zur Angebotsseite, nachdem ein Produkt erfolgreich zum Angebot "
"hinzugefügt wurde.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:226
msgid "Redirect After Quote Submission"
msgstr ""
"Nach Angebotsabgabe umleiten\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:231
msgid "Redirect to any page after Quote is submitted successfully."
msgstr ""
"Weiterleiten zu einer beliebigen Seite, nachdem das Angebot erfolgreich "
"gesendet wurde.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:241
msgid "URL of Page to Redirect after Quote Submission"
msgstr ""
"URL der Seite, die nach dem Einreichen des Angebots umgeleitet werden soll\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:246
msgid "URL of page to redirect after Quote is submitted successfully."
msgstr ""
"URL der Seite, die nach erfolgreicher Übermittlung des Angebots umgeleitet "
"werden soll.\n"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:265
msgid "Enable Captcha"
msgstr "Captcha aktivieren"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/afb2b_rfq_settings.php:270
msgid "Enable Google reCaptcha field on the Request a Quote Form."
msgstr ""
"Aktivieren Sie das Google reCaptcha-Feld auf dem Angebotsanfrage-Formular."

#: includes/afb2b_rfq_settings.php:316
msgid ""
"This will help you to hide price and add to cart and show request a quote "
"button for selected user roles including the guests users."
msgstr ""
"Auf diese Weise können Sie den Preis ausblenden, in den Warenkorb legen und "
"eine Angebotsschaltfläche für ausgewählte Benutzerrollen einschließlich der "
"Benutzer der Gäste anfordern.\n"

#: includes/afb2b_rfq_settings.php:333
msgid "---Choose Menu---"
msgstr "---Menü wählen---"

#: includes/afb2b_rfq_settings.php:450
msgid "This will help you to add fields on the quote form."
msgstr ""
"Auf diese Weise können Sie dem Angebotsformular Felder hinzufügen.\n"

#: includes/afb2b_rfq_settings.php:506
msgid "Name Field"
msgstr "Name Feld"

#: includes/afb2b_rfq_settings.php:514
msgid "Enable Name Field"
msgstr "Aktivieren Sie das Feld Name"

#: includes/afb2b_rfq_settings.php:519
msgid "Enable Name field on the Request a Quote Form."
msgstr "Aktivieren Sie das Feld Name auf dem Formular Angebotsanfrage."

#: includes/afb2b_rfq_settings.php:526 includes/afb2b_rfq_settings.php:619
#: includes/afb2b_rfq_settings.php:712 includes/afb2b_rfq_settings.php:807
#: includes/afb2b_rfq_settings.php:908 includes/afb2b_rfq_settings.php:1014
#: includes/afb2b_rfq_settings.php:1110 includes/afb2b_rfq_settings.php:1206
#: includes/afb2b_rfq_settings.php:1302
msgid "is Required?"
msgstr "ist ein erforderliches Feld?"

#: includes/afb2b_rfq_settings.php:531
msgid "Check if you want to make Name field required."
msgstr ""
"Markieren Sie, wenn Sie das Feld Name als obligatorisch kennzeichnen möchten."

#: includes/afb2b_rfq_settings.php:538 includes/afb2b_rfq_settings.php:631
#: includes/afb2b_rfq_settings.php:724 includes/afb2b_rfq_settings.php:819
#: includes/afb2b_rfq_settings.php:920 includes/afb2b_rfq_settings.php:1026
#: includes/afb2b_rfq_settings.php:1122 includes/afb2b_rfq_settings.php:1218
#: includes/afb2b_rfq_settings.php:1314
msgid "Sort Order"
msgstr "Sortierreihenfolge"

#: includes/afb2b_rfq_settings.php:543
msgid "Sort Order of the Name field."
msgstr "Sortierreihenfolge des Feldes Name."

#: includes/afb2b_rfq_settings.php:550 includes/afb2b_rfq_settings.php:643
#: includes/afb2b_rfq_settings.php:736 includes/afb2b_rfq_settings.php:831
#: includes/afb2b_rfq_settings.php:932 includes/afb2b_rfq_settings.php:1038
#: includes/afb2b_rfq_settings.php:1134 includes/afb2b_rfq_settings.php:1230
#: includes/afb2b_rfq_settings.php:1326
msgid "Label"
msgstr ""
"Etikette\n"

#: includes/afb2b_rfq_settings.php:555
msgid "Label of the Name field."
msgstr "Label des Feldes Name."

#: includes/afb2b_rfq_settings.php:599
msgid "Email Field"
msgstr "E-Mail-Feld"

#: includes/afb2b_rfq_settings.php:607
msgid "Enable Email Field"
msgstr "E-Mail-Feld aktivieren"

#: includes/afb2b_rfq_settings.php:612
msgid "Enable Email field on the Request a Quote Form."
msgstr "Aktivieren Sie das Feld E-Mail auf dem Formular Angebotsanfrage."

#: includes/afb2b_rfq_settings.php:624
msgid "Check if you want to make Email field required."
msgstr ""
"Markieren Sie das Feld E-Mail, wenn Sie es als obligatorisch kennzeichnen "
"möchten."

#: includes/afb2b_rfq_settings.php:636
msgid "Sort Order of the Email field."
msgstr "Sortierreihenfolge des Feldes E-Mail."

#: includes/afb2b_rfq_settings.php:648
msgid "Label of the Email field."
msgstr "Label des Feldes E-Mail."

#: includes/afb2b_rfq_settings.php:692
msgid "Company Field"
msgstr "Bereich Unternehmen"

#: includes/afb2b_rfq_settings.php:700
msgid "Enable Company Field"
msgstr "Feld Firma aktivieren"

#: includes/afb2b_rfq_settings.php:705
msgid "Enable Company field on the Request a Quote Form."
msgstr "Aktivieren Sie das Feld Unternehmen auf dem Formular Angebotsanfrage."

#: includes/afb2b_rfq_settings.php:717
msgid "Check if you want to make Company field required."
msgstr "Markieren Sie, wenn das Feld Firma obligatorisch sein soll."

#: includes/afb2b_rfq_settings.php:729
msgid "Sort Order of the Company field."
msgstr "Sortierreihenfolge des Feldes Firma."

#: includes/afb2b_rfq_settings.php:741
msgid "Label of the Company field."
msgstr "Label des Feldes Firma."

#: includes/afb2b_rfq_settings.php:786
msgid "Phone Field"
msgstr "Telefon Feld"

#: includes/afb2b_rfq_settings.php:795
msgid "Enable Phone Field"
msgstr "Telefon-Feld aktivieren"

#: includes/afb2b_rfq_settings.php:800
msgid "Enable Phone field on the Request a Quote Form."
msgstr "Aktivieren Sie das Feld Telefon auf dem Angebotsanfrageformular."

#: includes/afb2b_rfq_settings.php:812
msgid "Check if you want to make Phone field required."
msgstr ""
"Markieren Sie, wenn Sie das Feld Telefon als obligatorisch kennzeichnen "
"möchten."

#: includes/afb2b_rfq_settings.php:824
msgid "Sort Order of the Phone field."
msgstr "Sortierreihenfolge des Feldes Telefon."

#: includes/afb2b_rfq_settings.php:836
msgid "Label of the Phone field."
msgstr "Label des Feldes Telefon."

#: includes/afb2b_rfq_settings.php:888
msgid "File/Image Upload Field"
msgstr "Datei/Bild-Upload-Feld"

#. ID used to identify the field throughout the theme
#: includes/afb2b_rfq_settings.php:896 includes/tax-exempt/general.php:87
msgid "Enable File Upload Field"
msgstr "Datei-Upload-Feld aktivieren"

#: includes/afb2b_rfq_settings.php:901
msgid "Enable File/Image Upload field on the Request a Quote Form."
msgstr ""
"Aktivieren Sie das Feld Datei/Bild-Upload auf dem Formular Angebotsanfrage."

#: includes/afb2b_rfq_settings.php:913
msgid "Check if you want to make File/Image Upload field required."
msgstr ""
"Markieren Sie, wenn das Feld Datei/Bild-Upload obligatorisch sein soll."

#: includes/afb2b_rfq_settings.php:925
msgid "Sort Order of the File/Image Upload field."
msgstr "Sortierreihenfolge des Felds Datei/Bild-Upload."

#: includes/afb2b_rfq_settings.php:937
msgid "Label of the File/Image Upload field."
msgstr "Label des Feldes Datei/Bild-Upload."

#: includes/afb2b_rfq_settings.php:944
msgid "Allowed Types"
msgstr "Erlaubte Typen"

#: includes/afb2b_rfq_settings.php:949
msgid ""
"Allowed file upload types. e.g (png,jpg,txt). Add comma separated, please do "
"not use dot(.)."
msgstr ""
"Erlaubte Datei-Upload-Typen, z.B. (png,jpg,txt). Komma getrennt hinzufügen, "
"bitte keinen Punkt(.) verwenden."

#: includes/afb2b_rfq_settings.php:994
msgid "Message Field"
msgstr "Nachrichtenfeld"

#: includes/afb2b_rfq_settings.php:1002
msgid "Enable Message Field"
msgstr "Nachrichtenfeld aktivieren"

#: includes/afb2b_rfq_settings.php:1007
msgid "Enable Message field on the Request a Quote Form."
msgstr "Aktivieren Sie das Feld Nachricht auf dem Formular Angebotsanfrage."

#: includes/afb2b_rfq_settings.php:1019
msgid "Check if you want to make Message field required."
msgstr ""
"Markieren Sie das Feld Nachricht, wenn Sie es als obligatorisch kennzeichnen "
"möchten."

#: includes/afb2b_rfq_settings.php:1031
msgid "Sort Order of the Message field."
msgstr "Sortierreihenfolge des Feldes Nachricht."

#: includes/afb2b_rfq_settings.php:1043
msgid "Label of the Message field."
msgstr "Label des Feldes Nachricht."

#: includes/afb2b_rfq_settings.php:1089
msgid "Field 1"
msgstr "Feld 1"

#: includes/afb2b_rfq_settings.php:1098
msgid "Enable Field 1"
msgstr "Aktivieren Sie Feld 1"

#: includes/afb2b_rfq_settings.php:1103
msgid "Enable Additional Field 1 on the Request a Quote Form."
msgstr "Aktivieren Sie das zusätzliche Feld 1 auf dem Angebotsanfrageformular."

#: includes/afb2b_rfq_settings.php:1115
msgid "Check if you want to make Additional Field 1 field required."
msgstr ""
"Prüfen Sie, ob Sie das Feld Zusatzfeld 1 als obligatorisch kennzeichnen "
"möchten."

#: includes/afb2b_rfq_settings.php:1127
msgid "Sort Order of the Additional Field 1 field."
msgstr "Sortierreihenfolge des Feldes Zusatzfeld 1."

#: includes/afb2b_rfq_settings.php:1139
msgid "Label of the Additional Field 1 field."
msgstr ""
"Label des Feldes Zusatzfeld 1.\n"
"\t\n"

#: includes/afb2b_rfq_settings.php:1185 includes/afb2b_rfq_settings.php:1281
msgid "Field 2"
msgstr "Feld 2"

#: includes/afb2b_rfq_settings.php:1194
msgid "Enable Field 2"
msgstr "Aktivieren Sie Feld 2"

#: includes/afb2b_rfq_settings.php:1199
msgid "Enable Additional Field 2 on the Request a Quote Form."
msgstr "Aktivieren Sie das zusätzliche Feld 2 auf dem Angebotsanfrageformular."

#: includes/afb2b_rfq_settings.php:1211
msgid "Check if you want to make Additional Field 2 field required."
msgstr ""
"Prüfen Sie, ob Sie das Feld Zusatzfeld 2 als obligatorisch kennzeichnen "
"möchten."

#: includes/afb2b_rfq_settings.php:1223
msgid "Sort Order of the Additional Field 2 field."
msgstr "Sortierreihenfolge des Feldes Zusatzfeld 2."

#: includes/afb2b_rfq_settings.php:1235
msgid "Label of the Additional Field 2 field."
msgstr ""
"Label des Feldes Zusatzfeld 2.\n"
"\t\n"

#: includes/afb2b_rfq_settings.php:1290
msgid "Enable Field 3"
msgstr "Aktivieren Sie Feld 3"

#: includes/afb2b_rfq_settings.php:1295
msgid "Enable Additional Field 3 on the Request a Quote Form."
msgstr "Aktivieren Sie das zusätzliche Feld 3 auf dem Angebotsanfrageformular."

#: includes/afb2b_rfq_settings.php:1307
msgid "Check if you want to make Additional Field 3 field required."
msgstr ""
"Prüfen Sie, ob Sie das Feld Zusatzfeld 3 als obligatorisch kennzeichnen "
"möchten."

#: includes/afb2b_rfq_settings.php:1319
msgid "Sort Order of the Additional Field 3 field."
msgstr "Sortierreihenfolge des Feldes Zusatzfeld 3."

#: includes/afb2b_rfq_settings.php:1331
msgid "Label of the Additional Field 3 field."
msgstr ""
"Label des Feldes Zusatzfeld 3.\n"
"\t\n"

#: includes/afb2b_rfq_settings.php:1354
msgid ""
"Manage the redirect to quote page after Add to Quote and redirect to any "
"page after quote form submission ."
msgstr ""
"Verwalten Sie die Seite \"Weiterleiten zum Angebot\" nach \"Zum Angebot "
"hinzufügen\" und leiten Sie sie nach dem Absenden des Angebotsformulars auf "
"eine beliebige Seite weiter.\n"

#: includes/afb2b_rfq_settings.php:1389
msgid "Manage Google reCaptcha settings."
msgstr ""
"Verwalten Sie die Google reCaptcha-Einstellungen.\n"

#: includes/addify_quote_request_page.php:174
msgid "Remove this item"
msgstr "Dieses Element entfernen"

#: includes/addify_quote_request_page.php:380
msgid "Submit"
msgstr "Einreichen"

#: includes/addify_quote_request_page.php:390
msgid "Your quote is currently empty."
msgstr "Ihr Angebot ist derzeit leer."

#: includes/addify_quote_request_page.php:391
msgid "Return To Shop"
msgstr ""
"Zurück zum Shop\n"

#. ID used to identify the field throughout the theme
#: includes/addify-payments-by-user-roles.php:11
#: includes/payments/addify-payments-by-user-roles.php:11
msgid "Select Payement Method for User Roles"
msgstr ""
"Wählen Sie Zahlungsmethode für Benutzerrollen\n"

#: includes/addify-payments-by-user-roles.php:49
#: includes/shipping/addify-shipping-by-user-roles-settings.php:92
#: includes/payments/addify-payments-by-user-roles.php:49
#: includes/payments/addify-payments-by-user-roles.php:94
msgid "Select Payment Methods:"
msgstr ""
"Zahlungsmethoden auswählen:\n"

#: includes/csp_rule_level.php:9
#: additional_classes/class_afb2b_rfq_admin.php:88
#: additional_classes/class_afb2b_role_based_pricing_admin.php:348
msgid "Rule Priority"
msgstr "Regel Priorität"

#: includes/csp_rule_level.php:13
msgid ""
"Provide number between 0 and 100, If more than one rules are applied on same "
"item then rule with higher priority will be applied. 1 is high and 100 is "
"low."
msgstr ""
"Geben Sie eine Zahl zwischen 0 und 100 an. Wenn mehr als eine Regel auf "
"dasselbe Element angewendet wird, wird eine Regel mit höherer Priorität "
"angewendet. 1 ist hoch und 100 ist niedrig.\n"

#: includes/csp_rule_level.php:18
#: additional_classes/class_afb2b_rfq_admin.php:134
msgid "Apply on All Products"
msgstr "Auf alle Produkte anwenden"

#: includes/csp_rule_level.php:24
#: additional_classes/class_afb2b_rfq_admin.php:140
msgid "Check this if you want to apply this rule on all products."
msgstr ""
"Markieren Sie dies, wenn Sie diese Regel auf alle Produkte anwenden möchten."

#: additional_classes/class_afb2b_rfq_front.php:915
msgid " items in quote"
msgstr ""
"Artikel im Angebot\n"

#: additional_classes/class_afb2b_rfq_front.php:1036
msgid " View Quote"
msgstr "Zitat ansehen"

#: additional_classes/class_afb2b_rfq_front.php:1043
msgid "No products in quote basket."
msgstr "Keine Produkte im Angebotskorb."

#: additional_classes/class_afb2b_rfq_front.php:1073
msgid "has been added to your quote."
msgstr "wurde Ihrem Zitat hinzugefügt."

#: additional_classes/class_afb2b_rfq_front.php:1712
msgid "Sorry, your nonce did not verify."
msgstr "Tut mir leid, Ihr Nonce hat das nicht überprüft."

#: additional_classes/class_afb2b_rfq_front.php:1737
#: additional_classes/class_afb2b_rfq_front.php:1760
#: additional_classes/class_afb2b_rfq_front.php:1783
#: additional_classes/class_afb2b_rfq_front.php:1808
#: additional_classes/class_afb2b_rfq_front.php:1831
#: additional_classes/class_afb2b_rfq_front.php:1856
#: additional_classes/class_afb2b_rfq_front.php:1879
#: additional_classes/class_afb2b_rfq_front.php:1902
#: additional_classes/class_afb2b_rfq_front.php:1922
msgid " is a required field!"
msgstr "ist ein Pflichtfeld!"

#: additional_classes/class_afb2b_rfq_front.php:1954
msgid "Invalid file type!"
msgstr ""
"Ungültiger Dateityp!\n"

#: additional_classes/class_afb2b_rfq_front.php:1968
msgid "Invalid reCaptcha!"
msgstr "Ungültiges reCaptcha!"

#: additional_classes/class_afb2b_rfq_front.php:1971
msgid "reCaptcha is required!"
msgstr "reCaptcha ist erforderlich!"

#: additional_classes/class_afb2b_rfq_front.php:2211
msgid "Quote Info:"
msgstr "Angebot Info:"

#: additional_classes/class_afb2b_rfq_front.php:2221
#: additional_classes/class_afb2b_rfq_front.php:2727
msgid "SKU"
msgstr "SKU"

#: additional_classes/class_afb2b_rfq_front.php:2390
msgid "You have recieved a new quote request."
msgstr "Sie haben eine neue Offertanfrage erhalten."

#: additional_classes/class_afb2b_rfq_front.php:2401
msgid "Request a quote!"
msgstr ""
"Fordern Sie ein Angebot an!\n"

#: additional_classes/class_afb2b_rfq_front.php:2457
msgid "Failed! Unable to process your request."
msgstr "Gescheitert! Ihre Anfrage konnte nicht bearbeitet werden."

#: additional_classes/class_afb2b_rfq_front.php:2569
#: additional_classes/class_afb2b_rfq_front.php:2692
msgid "Quotes"
msgstr "Zitate"

#: additional_classes/class_afb2b_rfq_front.php:2716
#: additional_classes/class_afb2b_rfq_front.php:2718
msgid "Quote "
msgstr "Angebot"

#: additional_classes/class_afb2b_rfq_front.php:2716
#: additional_classes/class_afb2b_rfq_front.php:2718
#: additional_classes/class_afb2b_rfq_front.php:2885
msgid "#"
msgstr "#"

#: additional_classes/class_afb2b_rfq_front.php:2718
msgid " was placed on "
msgstr "wurde auf"

#: additional_classes/class_afb2b_rfq_front.php:2720
msgid "Quote Details"
msgstr "Details zum Angebot"

#: additional_classes/class_afb2b_rfq_front.php:2873
msgid "Quote"
msgstr "Angebot"

#: additional_classes/class_afb2b_rfq_front.php:2874
#: additional_classes/class_afb2b_rfq_admin.php:630
msgid "Date"
msgstr "Datum"

#: additional_classes/class_afb2b_rfq_front.php:2875
msgid "Action"
msgstr "Aktion"

#: additional_classes/class_afb2b_rfq_front.php:2893
#: additional_classes/class_afb2b_rfq_admin.php:660
msgid "View"
msgstr "Siehe"

#: additional_classes/class_afb2b_rfq_front.php:2906
msgid "Go to shop"
msgstr ""
"Zum Laden gehen\n"

#: additional_classes/class_afb2b_rfq_front.php:2906
msgid "No quote has been made yet."
msgstr "Es wurde noch kein Angebot abgegeben."

#: additional_classes/class_afb2b_rfq_admin.php:55
msgid "Rule Settings"
msgstr "Regel-Einstellungen"

#: additional_classes/class_afb2b_rfq_admin.php:77
#: additional_classes/class_afb2b_rfq_admin.php:599
msgid "Rule Type"
msgstr "Regel-Typ"

#: additional_classes/class_afb2b_rfq_admin.php:80
#: additional_classes/class_afb2b_rfq_admin.php:613
msgid "Quote Rule for Guest Users"
msgstr "Angebotsregel für Gast-Benutzer"

#: additional_classes/class_afb2b_rfq_admin.php:81
#: additional_classes/class_afb2b_rfq_admin.php:611
msgid "Quote Rule for Registered Users"
msgstr "Angebotsregel für registrierte Benutzer"

#: additional_classes/class_afb2b_rfq_admin.php:93
msgid ""
"Provide value from high priority 1 to Low priority 10. If more than one rule "
"are applied on same item rule with high priority will be applied."
msgstr ""
"Geben Sie einen Wert von hoher Priorität 1 bis niedriger Priorität 10 an. "
"Wenn mehr als eine Regel auf denselben Artikel angewendet wird, wird die "
"Regel mit hoher Priorität angewendet."

#: additional_classes/class_afb2b_rfq_admin.php:101
msgid "Quote for User Roles"
msgstr "Angebot für Benutzerrollen"

#: additional_classes/class_afb2b_rfq_admin.php:146
msgid "Quote Rule for Selected Products"
msgstr "Angebotsregel für ausgewählte Produkte"

#: additional_classes/class_afb2b_rfq_admin.php:173
msgid "Quote Rule for Selected Categories"
msgstr "Angebotsregel für ausgewählte Kategorien"

#: additional_classes/class_afb2b_rfq_admin.php:409
msgid "Hide Price"
msgstr "Preis ausblenden"

#: additional_classes/class_afb2b_rfq_admin.php:414
msgid "No"
msgstr "Nein\t"

#: additional_classes/class_afb2b_rfq_admin.php:415
msgid "Yes"
msgstr "Ja"

#: additional_classes/class_afb2b_rfq_admin.php:424
msgid "Hide Price Text"
msgstr "Preistext ausblenden"

#: additional_classes/class_afb2b_rfq_admin.php:436
msgid "Display the above text when price is hidden, e.g \"Price is hidden\""
msgstr ""
"Den obigen Text anzeigen, wenn der Preis versteckt ist, z.B. \"Preis ist "
"versteckt\"."

#: additional_classes/class_afb2b_rfq_admin.php:444
msgid "Hide Add to Cart Button"
msgstr "Schaltfläche Zum Warenkorb hinzufügen ausblenden"

#: additional_classes/class_afb2b_rfq_admin.php:450
msgid "Replace Add to Cart button with a Quote Button"
msgstr ""
"Schaltfläche \"In den Warenkorb legen\" durch eine Schaltfläche \"Zitat\" "
"ersetzen"

#: additional_classes/class_afb2b_rfq_admin.php:454
msgid "Keep Add to Cart button and add a new Quote Button"
msgstr ""
"Behalten Sie die Schaltfläche \"In den Warenkorb\" bei und fügen Sie eine "
"neue Schaltfläche \"Angebot\" hinzu"

#: additional_classes/class_afb2b_rfq_admin.php:458
msgid "Replace Add to Cart with custom button"
msgstr "In den Warenkorb legen durch benutzerdefinierte Schaltfläche ersetzen"

#: additional_classes/class_afb2b_rfq_admin.php:463
msgid "Keep Add to Cart and add a new custom button"
msgstr ""
"Behalten Sie Add to Cart bei und fügen Sie eine neue benutzerdefinierte "
"Schaltfläche\n"
"\n"

#: additional_classes/class_afb2b_rfq_admin.php:473
msgid "Custom Button Link"
msgstr "Benutzerdefinierter Button-Link"

#: additional_classes/class_afb2b_rfq_admin.php:485
msgid "Link for custom button e.g \"http://www.example.com\""
msgstr ""
"Link für benutzerdefinierte Schaltfläche z.B. \"http://www.example.com\"."

#: additional_classes/class_afb2b_rfq_admin.php:493
msgid "Custom Button Label"
msgstr "Benutzerdefinierte Tastenbeschriftung"

#: additional_classes/class_afb2b_rfq_admin.php:505
msgid "Display the above label on custom button, e.g \"Request a Quote\""
msgstr ""
"Das obige Etikett auf der benutzerdefinierten Schaltfläche anzeigen, z.B. "
"\"Angebot anfordern\"."

#: additional_classes/class_afb2b_rfq_admin.php:600
#: additional_classes/class_afb2b_role_based_pricing_admin.php:349
msgid "Date Published"
msgstr "Datum der Veröffentlichung"

#: additional_classes/class_afb2b_rfq_admin.php:627
msgid "Quote #"
msgstr "Angebot #"

#: additional_classes/class_afb2b_rfq_admin.php:628
msgid "Customer Name"
msgstr "Kunden-E-Mail"

#: additional_classes/class_afb2b_rfq_admin.php:629
msgid "Customer Email"
msgstr "Kunden-E-Mail"

#: additional_classes/class_afb2b_rfq_admin.php:660
msgid "View this item"
msgstr "Diesen Artikel anzeigen"

#: additional_classes/class_afb2b_role_based_pricing_admin.php:238
msgid "Rule Details"
msgstr "Details zur Regel"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/exempt_customers_roles.php:12
msgid "Choose Customers"
msgstr ""
"Wählen Sie Kunden\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/exempt_customers_roles.php:17
#: includes/tax-exempt/general.php:17
msgid "Enable/Disable tax for tax-exempted and approved users."
msgstr ""
"Aktivieren / Deaktivieren der Steuer für steuerbefreite und genehmigte "
"Benutzer.\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/exempt_customers_roles.php:32
msgid "Choose user roles to grant them tax exemption status."
msgstr ""
"Wählen Sie Benutzerrollen, um ihnen den Steuerbefreiungsstatus zu gewähren.\n"

#: includes/tax-exempt/exempt_customers_roles.php:45
msgid ""
"In this section, you can specify the customers and user roles who are "
"exempted from tax. These customers and roles are not required to fill the "
"tax form from \"My Account\" page."
msgstr ""
"In diesem Abschnitt können Sie die Kunden und Benutzerrollen angeben, die "
"von der Steuer befreit sind. Diese Kunden und Rollen müssen das "
"Steuerformular auf der Seite \"Mein Konto\" nicht ausfüllen.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/guest_user.php:12
msgid "Show tax exemption message"
msgstr ""
"Steuerbefreiungsnachricht anzeigen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/guest_user.php:17
msgid ""
"If this option is checked then a message will be displayed for guest user "
"about tax exemption."
msgstr ""
"Wenn diese Option aktiviert ist, wird dem Gastbenutzer eine Meldung zur "
"Steuerbefreiung angezeigt.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/guest_user.php:27
#: includes/tax-exempt/exempt_request.php:58
msgid "Message Text"
msgstr ""
"Nachrichtentext\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/guest_user.php:32
msgid "This message will be displayed for guest users."
msgstr ""
"Diese Nachricht wird für Gastbenutzer angezeigt.\n"

#: includes/tax-exempt/guest_user.php:43
msgid "Show tax exemption message for guest users."
msgstr ""
"Steuerbefreiungsnachricht für Gastbenutzer anzeigen.\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:17
msgid ""
"All admin emails that are related to our module will be sent to this email "
"address."
msgstr ""
"Alle administrativen E-Mails, die sich auf unser Modul beziehen, werden an "
"diese E-Mail-Adresse gesendet. "

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:32
msgid ""
"This message will be shown when user add or update tax info in my account."
msgstr ""
"Diese Meldung wird angezeigt, wenn der Benutzer Steuerinformationen in "
"meinem Konto hinzufügt oder aktualisiert.\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:48
msgid ""
"This subject will be used when a user add or update tax info from my account."
msgstr ""
"Dieser Betreff wird verwendet, wenn ein Benutzer Steuerinformationen aus "
"meinem Konto hinzufügt oder aktualisiert.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/email_notification.php:58
msgid "Admin Email Message"
msgstr ""
"Admin-E-Mail-Nachricht\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:63
msgid ""
"This message will be used when a user add or update tax info from my account."
msgstr ""
"Diese Nachricht wird verwendet, wenn ein Benutzer Steuerinformationen aus "
"meinem Konto hinzufügt oder aktualisiert.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/email_notification.php:73
msgid "Approve Tax Info Email Subject"
msgstr ""
"E-Mail-Betreff für Steuerinformationen genehmigen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:78
msgid "This subject will be used when admin approves submitted tax info."
msgstr ""
"Dieser Betreff wird verwendet, wenn der Administrator die eingereichten "
"Steuerinformationen genehmigt.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/email_notification.php:88
msgid "Approve Tax Info Email Message"
msgstr ""
"E-Mail-Nachricht mit Steuerinformationen genehmigen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:93
msgid "This message will be used when admin approves submitted tax info."
msgstr ""
"Diese Nachricht wird verwendet, wenn der Administrator die übermittelten "
"Steuerinformationen genehmigt.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/email_notification.php:103
msgid "Disapprove Tax Info Email Subject"
msgstr ""
"E-Mail-Betreff für Steuerinformationen ablehnen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:108
msgid "This subject will be used when admin disapprove submitted tax info."
msgstr ""
"Dieser Betreff wird verwendet, wenn der Administrator die eingereichten "
"Steuerinformationen ablehnt.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/email_notification.php:118
msgid "Disapprove Tax Info Email Message"
msgstr ""
"E-Mail-Nachricht mit Steuerinformationen ablehnen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/email_notification.php:123
msgid "This message will be used when admin disapprove submitted tax info."
msgstr ""
"Diese Nachricht wird verwendet, wenn der Administrator die übermittelten "
"Steuerinformationen ablehnt.\n"

#: includes/tax-exempt/email_notification.php:134
msgid "Email & Notification Settings"
msgstr ""
"E-Mail- und Benachrichtigungseinstellungen\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:12
msgid "Remove Tax Automatically"
msgstr ""
"Steuern automatisch entfernen\n"

#: includes/tax-exempt/general.php:17
msgid ""
"Enable this checkbox will disable tax for all tax-exempted and approved "
"users."
msgstr ""
"Durch Aktivieren dieses Kontrollkästchens wird die Steuer für alle "
"steuerbefreiten und genehmigten Benutzer deaktiviert.\n"

#: includes/tax-exempt/general.php:17
msgid ""
"Disable this checkbox will show a checkbox in the checkout page to notify "
"them that tax exemption is available"
msgstr ""
"Wenn Sie dieses Kontrollkästchen deaktivieren, wird auf der Checkout-Seite "
"ein Kontrollkästchen angezeigt, um sie darüber zu informieren, dass eine "
"Steuerbefreiung verfügbar ist\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:27
msgid "Enable Text Field"
msgstr ""
"Textfeld aktivieren\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/general.php:32
msgid ""
"This text field will be shown in tax form in user my account page. This "
"field can be used to collect name, tax id etc."
msgstr ""
"Dieses Textfeld wird im Steuerformular auf der Benutzerseite meines Kontos "
"angezeigt. In diesem Feld können Name, Steuer-ID usw. erfasst werden.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:42
msgid "Text Field Label"
msgstr ""
"Textfeldbeschriftung\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/general.php:47
msgid "Label of text field."
msgstr ""
"Beschriftung des Textfeldes.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:57
msgid "Enable Textarea Field"
msgstr ""
"Textfeld aktivieren\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/general.php:62
msgid ""
"This textarea field will be shown in tax form in user my account page. This "
"field can be used to collect additional info etc."
msgstr ""
"Dieses Textfeld wird im Steuerformular auf der Seite \"Benutzer meines "
"Kontos\" angezeigt. In diesem Feld können zusätzliche Informationen usw. "
"gesammelt werden.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:72
msgid "Textarea Field Label"
msgstr ""
"Textfeld Feldbeschriftung\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/general.php:77
msgid "Label of textarea field."
msgstr ""
"Beschriftung des Textfeldfeldes.\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/general.php:92
msgid ""
"This file upload field will be shown in tax form in user my account page. "
"This field can be used to collect tax certificate etc."
msgstr ""
"Dieses Feld zum Hochladen von Dateien wird im Steuerformular auf der "
"Benutzerseite meines Kontos angezeigt. Dieses Feld kann zum Sammeln von "
"Steuerbescheinigungen usw. verwendet werden.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:102
msgid "File Upload Field Label"
msgstr ""
"Feldbezeichnung für Datei-Upload\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/general.php:107 includes/tax-exempt/general.php:122
msgid "Label of file upload field."
msgstr ""
"Bezeichnung des Feldes zum Hochladen der Datei.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/general.php:117
msgid "Allowed File Types"
msgstr "Erlaubte Dateitypen"

#: includes/tax-exempt/general.php:135
msgid ""
"In general settings you can enable/disable tax for specific users and choose "
"which field(s) you want to show on the tax exemption request form."
msgstr ""
"In den allgemeinen Einstellungen können Sie die Steuer für bestimmte "
"Benutzer aktivieren / deaktivieren und auswählen, welche Felder auf dem "
"Antragsformular für Steuerbefreiungen angezeigt werden sollen.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/exempt_request.php:27
msgid "Auto Approve Tax Exempt Request"
msgstr ""
"Steuerbefreiungsanforderung automatisch genehmigen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/exempt_request.php:32
msgid ""
"If this option is checked then tax exempt requests will be auto-approved and "
"users of above selected user roles will be eligible for tax exempt right "
"after submitting the info."
msgstr ""
"Wenn diese Option aktiviert ist, werden Steuerbefreiungsanfragen automatisch "
"genehmigt, und Benutzer der oben ausgewählten Benutzerrollen können direkt "
"nach dem Absenden der Informationen von der Steuer befreit werden.\n"

#. ID used to identify the field throughout the theme
#: includes/tax-exempt/exempt_request.php:43
msgid "Show Tax Exemption Message on Checkout Page"
msgstr ""
"Steuerbefreiungsnachricht auf der Checkout-Seite anzeigen\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/exempt_request.php:48
msgid ""
"If this option is checked then a message will be displayed for the above "
"selected user role users about tax exemption."
msgstr ""
"Wenn diese Option aktiviert ist, wird für die oben ausgewählten "
"Benutzerrollenbenutzer eine Meldung zur Steuerbefreiung angezeigt.\n"

#. The array of arguments to pass to the callback. In this case, just a description.
#: includes/tax-exempt/exempt_request.php:63
msgid "This will be visible for the user roles customer has selected above."
msgstr ""
"Dies wird für die Benutzerrollen angezeigt, die der Kunde oben ausgewählt "
"hat.\n"

#: includes/tax-exempt/exempt_request.php:74
msgid ""
"Select user roles for whom you want to display tax exemption form in \"My "
"Account\" page."
msgstr ""
"Wählen Sie auf der Seite \"Mein Konto\" Benutzerrollen aus, für die Sie das "
"Steuerbefreiungsformular anzeigen möchten.\n"

#. ID used to identify the field throughout the theme
#: includes/shipping/addify-shipping-by-user-roles-settings.php:11
msgid "Select Shipping Methods for User Roles"
msgstr ""
"Wählen Sie Versandmethoden für Benutzerrollen\n"

#: includes/shipping/addify-shipping-by-user-roles-settings.php:47
msgid "Select Shipping Methods:"
msgstr ""
"Versandmethoden auswählen:\n"

#: includes/shipping/addify-shipping-by-user-roles-settings.php:84
#: includes/payments/addify-payments-by-user-roles.php:86
msgid "Guest"
msgstr ""
"Gast\n"

#. Name of the plugin
msgid "B2B for WooCommerce"
msgstr ""
"B2B für WooCommerce\n"

#. Description of the plugin
msgid ""
"WooCommerce B2b Plugin. (PLEASE TAKE BACKUP BEFORE UPDATING THE PLUGIN)."
msgstr ""
"WooCommerce B2b Plugin. (BITTE SICHERN, BEVOR SIE DEN PLUGIN AKTUALISIEREN)."
"\n"

#. URI of the plugin
msgid "https://woocommerce.com/products/b2b-for-woocommerce/"
msgstr "https://woocommerce.com/products/b2b-for-woocommerce/"

#. Author of the plugin
msgid "Addify"
msgstr "Addify"

#. Author URI of the plugin
msgid "http://www.addifypro.com"
msgstr "http://www.addifypro.com"
