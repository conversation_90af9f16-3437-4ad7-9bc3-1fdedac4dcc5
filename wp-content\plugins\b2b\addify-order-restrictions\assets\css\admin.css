form.afrfb_options_form th{
	vertical-align: middle;
}

table.addify-table-optoin {
	width: 100%;
	border-collapse: collapse;
}

tr.addify-option-field th {
	width: 27% !important;
	padding: 20px 60px 20px 0;
	opacity: 0.85;
    font-size: 11px;
}

iframe.afrfb_file_preview{

	width: 100%;
    height: 400px;
}

tr.addify-option-field td{
	width: 70%;
	padding: 7px 0px;
}

tr.addify-option-field td span.description{
	opacity: 0.8;
}
.addify-option-field th .option-head h3 {
	text-align: left;
	margin: 8px 0px;
}
.addify-option-field th span.description p {
	font-style: normal;
	text-align: left;
	font-weight: 100;
}
.addify-option-field td .addify-input-field {
	width: auto;
	background: rgba(204, 204, 204, 0.22);
	line-height: 1.75em;
	padding-left: 0.75em;
}

tr.addify-option-field:not(.no_border) {
	border-bottom: 1px solid whitesmoke;
}

div.afrfb-metabox-fields input[type="email"], 
div.afrfb-metabox-fields input[type="tel"],
div.afrfb-metabox-fields input[type="date"], 
div.afrfb-metabox-fields input[type="text"],
div.afrfb-metabox-fields input[type="url"],
div.afrfb-metabox-fields select,
div.afrfb-metabox-fields textarea{
	width: 60%;
	margin-bottom: 0.246em;
}

div.afrfb-metabox-fields input[type="number"]{
	height: auto;
}

div.afrfb-metabox-fields .addify-option-field .select2-container{
	width: 60% !important;
}