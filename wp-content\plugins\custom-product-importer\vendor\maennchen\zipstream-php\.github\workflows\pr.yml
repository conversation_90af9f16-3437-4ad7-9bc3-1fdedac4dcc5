on:
  pull_request:
    branches:
      - "*"
  workflow_dispatch: {}

name: "Pull Request"

permissions:
  contents: read

jobs:
  test:
    name: "Test"

    permissions:
      contents: read
      security-events: write

    uses: ./.github/workflows/part_test.yml

  docs:
    name: "Doc<PERSON>"

    uses: ./.github/workflows/part_docs.yml

  dependabot:
    name: "Dependabot"

    if: ${{ github.actor == 'dependabot[bot]'}}

    permissions:
      pull-requests: write
      contents: write

    uses: ./.github/workflows/part_dependabot.yml

  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit

      - name: 'Checkout Repository'
        uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1
      - name: 'Dependency Review'
        uses: actions/dependency-review-action@5a2ce3f5b92ee19cbb1541a4984c76d921601d7c # v4.3.4
