{"name": "phpseclib/phpseclib", "type": "library", "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "keywords": ["security", "crypto", "cryptography", "encryption", "signature", "signing", "rsa", "aes", "blowfish", "twofish", "ssh", "sftp", "x509", "x.509", "asn1", "asn.1", "BigInteger"], "homepage": "http://phpseclib.sourceforge.net", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=5.0.0"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a wide variety of cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 5.0.0."}, "include-path": ["phpseclib/"], "autoload": {"psr-0": {"Crypt": "phpseclib/", "File": "phpseclib/", "Math": "phpseclib/", "Net": "phpseclib/", "System": "phpseclib/"}, "files": ["phpseclib/bootstrap.php", "phpseclib/Crypt/Random.php"]}}