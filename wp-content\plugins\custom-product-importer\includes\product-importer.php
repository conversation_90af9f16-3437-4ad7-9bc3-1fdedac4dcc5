<?php

function save_or_update_woocommerce_product($product_data)
{
    // Use the 'Product' field from the Excel as product name or SKU
    $product_name = $product_data[$_SESSION['header_indices']['Product']];
    $product_SKU = $product_data[$_SESSION['header_indices']['Product']];
    $description = isset($_SESSION['header_indices']['Description']) ? $product_data[$_SESSION['header_indices']['Description']] : '';
    $product_brand = $product_data[$_SESSION['header_indices']['Brand']];
    $product_family = $product_data[$_SESSION['header_indices']['Product Family']];
    $product_series = $product_data[$_SESSION['header_indices']['Product Series']];
    $product_line = $product_data[$_SESSION['header_indices']['Product Line']];
    $model_size = $product_data[$_SESSION['header_indices']['Model/Size']];
    $do_not_show = $product_data[$_SESSION['header_indices']['Do Not Show']];

    $product_brand_logo = isset($_SESSION['header_indices']['Brand Logo']) ? $product_data[$_SESSION['header_indices']['Brand Logo']] : '';
    $image_url = isset($_SESSION['header_indices']['Product Image']) ? $product_data[$_SESSION['header_indices']['Product Image']] : '';
    // Check if product exists by SKU (if SKU is unique in the Excel data)
    // if (check_product_sku($product_SKU)) {
    $product_id = wc_get_product_id_by_sku($product_SKU); // Or you can search by name
    if ($product_id) {
        // Product exists, update it
        $product = wc_get_product($product_id);
        $product->set_description($description);
        $product->set_sku($product_name);
        $product->set_name($product_name);
        update_post_meta($product_id, '_brand', $product_brand);
        update_post_meta($product_id, '_product_family', $product_family);
        update_post_meta($product_id, '_product_series', $product_series);
        update_post_meta($product_id, '_product_line', $product_line);
        update_post_meta($product_id, '_model_size', $model_size);
        update_post_meta($product_id, '_do_not_show', $do_not_show);
        set_product_category($product_id, $product_brand, $product_brand_logo);
        set_product_image($product_id, $image_url);
        $product->save();
    } else {
        // Product doesn't exist, create a new one
        $product = new WC_Product();
        $product->set_name($product_name);
        $product->set_description($description);
        $product->set_sku($product_name);
        $product->save();

        add_post_meta($product->get_id(), '_brand', $product_brand);
        add_post_meta($product->get_id(), '_product_family', $product_family);
        add_post_meta($product->get_id(), '_product_series', $product_series);
        add_post_meta($product->get_id(), '_product_line', $product_line);
        add_post_meta($product->get_id(), '_model_size', $model_size);
        add_post_meta($product->get_id(), '_do_not_show', $do_not_show);
        set_product_category($product->get_id(), $product_brand, $product_brand_logo);
        set_product_image($product->get_id(), $image_url);
    }
    // }
    
    
}

function check_product_sku($product_SKU) {
    global $wpdb;

    // Table name (use the correct table prefix)
    $table_name = $wpdb->prefix . 'price_list'; // Example: wp_price_list

    // Query to check if the SKU exists
    $result = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE product_sku = %s", 
        $product_SKU
    ));

    // Return true if SKU exists, false otherwise
    return $result > 0;
}

function tmp_product_description_upload($tmp_batch){
    $sku_array = [];
    $description_array = [];

    foreach($tmp_batch as $product){
 
        $sku_array[] = $product[$_SESSION['header_indices']['Product']];
        $description_array[] = $product[$_SESSION['header_indices']['Description']];
    }

    global $wpdb;
    // Step 1: Fetch product IDs based on SKUs from wp_postmeta
    $sku_placeholders = implode(',', array_fill(0, count($sku_array), '%s'));
    $query = "
        SELECT post_id, meta_value 
        FROM {$wpdb->postmeta} 
        WHERE meta_key = '_sku' AND meta_value IN ($sku_placeholders)
    ";
    $query_results = $wpdb->get_results(
        $wpdb->prepare($query, ...$sku_array),
        ARRAY_A
    );

    $sku_to_id = [];
    foreach ($query_results as $row) {
        $sku_to_id[$row['meta_value']] = $row['post_id'];
    }

    $batch_values = [];
    foreach ($sku_array as $index => $sku) {
        if (isset($sku_to_id[$sku])) {
            $post_id = $sku_to_id[$sku];
            $description = sanitize_text_field($description_array[$index]);  // Clean the description text

            // Prepare the values for the query
            $batch_values[] = $wpdb->prepare("(%d, '_description', %s)", $post_id, $description);
        }
    }
    if (!empty($batch_values)) {
        $values_string = implode(',', $batch_values);
    
        // Batch query to insert or update the postmeta table
        $batch_query = "
            INSERT INTO {$wpdb->prefix}postmeta (post_id, meta_key, meta_value) 
            VALUES $values_string
            ON DUPLICATE KEY UPDATE meta_value = VALUES(meta_value);
        ";
    
        // Step 4: Execute the query
        $wpdb->query($batch_query);
    
        echo 'Descriptions updated successfully!';
    } else {
        echo 'No valid SKUs found to update.';
    }
}

function set_product_category($product_id, $category_name, $category_image_url = '')
{
    if (empty($category_name)) {
        return; // Skip if no category is provided
    }
    $category = get_term_by('name', $category_name, 'product_cat');
    if (empty($category)) {
        // Category doesn't exist, so create it
        $category_result = wp_insert_term($category_name, 'product_cat');

        if (!is_wp_error($category_result)) {
            $category_id = $category_result['term_id'];
            if (!empty($category_image_url)) {
                set_category_image($category_id, $category_image_url);
            }
        } else {
            // Handle the error if needed
            $log['error'] = $category_result->get_error_message();
            return;
        }
    } else {
        // Category exists, get the term ID
        $category_id = $category->term_id;
        if (!empty($category_image_url)) {
            set_category_image($category_id, $category_image_url);
        }
    }
    wp_set_object_terms($product_id, $category_id, 'product_cat');
}

function set_category_image($category_id, $image_url)
{
    $attachment_id = upload_image_from_url($category_id, $image_url);

    if ($attachment_id) {
        update_term_meta($category_id, 'thumbnail_id', $attachment_id);
    }
}

function set_product_image($product_id, $image_url)
{

    if (empty($image_url)) {
        return; // No image URL provided
    }
    // Upload the image from URL and get attachment ID
    $attachment_id = upload_image_from_url($product_id, $image_url);
    if ($attachment_id) {
        // Set the image as the featured image for the product
        set_post_thumbnail($product_id, $attachment_id);
    } else {
        esc_js(`alert('asdfasdfafda')`);
    }
}
function upload_image_from_url($product_id, $image_url)
{
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');

    $tmp_file = download_url($image_url);

    // Check if the download was successful
    if (is_wp_error($tmp_file)) {
        error_log('Download error: ' . $tmp_file->get_error_message());
        return false;  // Return false if there's an error
    }

    $file_array = array(
        'name'     => basename($image_url), // Get the file name from the URL
        'tmp_name' => $tmp_file,            // Temporary file name
    );

    // Step 3: Upload the file to the media library using media_handle_sideload()
    $attachment_id = media_handle_sideload($file_array, $product_id);

    // If upload failed, return error
    if (is_wp_error($attachment_id)) {
        error_log('Upload error: ' . $attachment_id->get_error_message());
        @unlink($tmp_file); // Clean up the temporary file
        return false;
    }

    // Clean up the temporary file
    @unlink($tmp_file);
    // Return the attachment ID on success
    return $attachment_id;
}
