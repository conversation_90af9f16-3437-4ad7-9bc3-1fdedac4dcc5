{"name": "rosell-dk/file-util", "description": "Functions for dealing with files and paths", "type": "library", "license": "MIT", "keywords": ["files", "path", "util"], "scripts": {"ci": ["@test", "@phpcs-all", "@composer validate --no-check-all --strict", "@phpstan-global"], "phpunit": "phpunit --coverage-text", "test": "phpunit --coverage-text=build/coverage.txt --coverage-clover=build/coverage.clover --coverage-html=build/coverage --whitelist=src tests", "test-no-cov": "phpunit --no-coverage tests", "test-41": "phpunit --no-coverage --configuration 'phpunit-41.xml.dist'", "test-with-coverage": "phpunit --coverage-text --configuration 'phpunit-with-coverage.xml.dist'", "test-41-with-coverage": "phpunit --coverage-text --configuration 'phpunit-41.xml.dist'", "cs-fix-all": ["php-cs-fixer fix src"], "cs-fix": "php-cs-fixer fix", "cs-dry": "php-cs-fixer fix --dry-run --diff", "phpcs": "phpcs --standard=phpcs-ruleset.xml", "phpcs-all": "phpcs --standard=phpcs-ruleset.xml src", "phpcbf": "phpcbf --standard=PSR2", "phpstan": "vendor/bin/phpstan analyse src --level=4", "phpstan-global-old": "~/.composer/vendor/bin/phpstan analyse src --level=4", "phpstan-global": "~/.config/composer/vendor/bin/phpstan analyse src --level=4"}, "extra": {"scripts-descriptions": {"ci": "Run tests before CI", "phpcs": "Checks coding styles (PSR2) of file/dir, which you must supply. To check all, supply 'src'", "phpcbf": "Fix coding styles (PSR2) of file/dir, which you must supply. To fix all, supply 'src'", "cs-fix-all": "Fix the coding style of all the source files, to comply with the PSR-2 coding standard", "cs-fix": "Fix the coding style of a PHP file or directory, which you must specify.", "test": "Launches the preconfigured PHPUnit"}}, "autoload": {"psr-4": {"FileUtil\\": "src/"}}, "autoload-dev": {"psr-4": {"FileUtil\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.bitwise-it.dk/contact", "role": "Project Author"}], "require": {"php": ">=5.4", "rosell-dk/exec-with-fallback": "^1.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "3.*", "phpstan/phpstan": "^1.5", "mikey179/vfsstream": "^1.6"}, "config": {"sort-packages": true}}