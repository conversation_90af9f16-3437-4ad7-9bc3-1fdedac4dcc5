*** B2B for WooCommerce Change Log***
2024-07-022 - version 3.0.4
* Fix - Bug fixed with add quote for guest users.

2024-05-31 - version 3.0.3
* Fix - Fixed bug with VAT field.

2024-05-23 - version 3.0.2
* Fix - Fixed bug with logo upload in request a quote.

2024-04-30 - version 3.0.1
* Fix - Fixed PDF Layout tabs not showing in settings.
* Fix - Fixed default fields not saving issue.

2024-04-24 - version 3.0.0
* Feature - Added dependable fields support in registration form.
* Feature - Added option to generate PDF quotes.
* Feature - Ability to create new user roles within the extension settings.
* Fix - Fixed translation issues.

2024-03-20 - version 2.4.0
* Feature - Added feature to setup cart level discount rules.

2023-08-16 - version 2.3.1
* Fix - Fixed registration issue.

2023-08-10 - version 2.3.0
* Update - Compatible with WooCommerce High-Performance Order Storage (HPOS).
* Update - Compatibility updated for latest versions of WooCommerce and WordPress.

2023-04-03 - version 2.2.4
* Fix - Blocked access to quote page via direct link.
* Fix - Fixed translation issue for "SKU".
* Fix - Fixed quote success message issue for guest users.

2023-03-29 - version 2.2.3
* Add - Added quote information on order detail page when quote is converted to order.
* Add - Added order ID on quote detail page when a quote is converted to order.

2023-03-27 - version 2.2.2
* Fix - Fixed double Tax bug on quote page.
* Fix - Fixed nonce issue when customer converts quote to order.
* Update - Force standard price to order when offered price is not set in admin quote details page. 

2023-03-24 - version 2.2.1
* Fix - Typo fixed.

2023-03-22 - version 2.2.0
* Update - compatibility with WooCommerce Brands Plugin.

2022-11-08 - version 2.1.2
* Fix - Fix role based pricing variations not saving correctly.
* Fix - Bug fixed with default fields.

2022-10-20 - version 2.1.1
* Fix - Fixed the issue of undefined index of user roles in role based pricing.
* Fix - Fixed the issue of undefined index of user roles in product visibility.
* Fix - Fixed the issue of issue of undefined method in RFQ-Product addons compatibility.
* Add - Added VIES validation using SOAP.
* Fix - Fixed nonce issues.

2022-06-03 - version 2.1.0
* Added the field type vat in user registration fields. 
* Added the admin order compatibility in role based pricing. 
* Fixed the issue of em tag color in tax exempt css. 
* Fixed the issue of the required field error on my account page. 
* Fixed the issue of reCaptcha validation when the secret key is missing.

2022-02-15 - version 2.0.2
* Fixed uploaded files delete issue on plugin update in Tax Exempt.

2022-02-04 - version 2.0.1
* Fixed issue with custom button link in Request a Quote.
* Fixed issue with the display value of user role in my account page.
* Fixed issue with the template override in emails.

2021-12-31 - version 2.0.0
* New Feature: Order restrictions by user role.
* Rule based prices now reflects on Request a quote page.
* Fixed the issue of role based pricing in CSV import for variable products.
* Fixed the issue of admin emails for approve/disapprove customers account in WooCommerce 6.0.0
* Fixed the issue of role based pricing error at product level.
* Fixed the issue of error on fresh installation in product visibility.

2021-12-11 - version 1.9.1
* Fixed bug undefined index in product level customer.

2021-10-11 - version 1.9.0
* Added feature to import role based prices using CSV file.

2021-09-15 - version 1.8.7
* Added compatibility with Aelia Currency Switcher in role base pricing.
* Added compatibility with WooCommerce Brands Plugin in role base pricing.
* Fixed cart price issue when more than one pricing rules are created for same product/category but different user roles.

2021-08-11 - version 1.8.6
* Fixed uploaded file access issue.
* Fixed Email admin email issue.
* Added feature to update role in my account.
* Added email notification for admin to receive an alert when a user updates custom fields data from their My Account page.
* Fixed duplicate fields issue appearing in my account.

2021-07-22 - version 1.8.5
* Fixed PHP error issue in admin all users listing page.

2021-07-14 - version 1.8.4
* Email templates in WooCommerce Email settings for User Registration.
* Saved uploaded files outside the extension folder in user registration..
* Do not display user status if pending approval option is turned off from extension settings in user registration.
* Fixed state dropdown field issue.

2021-06-22 - version 1.8.3
* Bug fix - empty fields issue on my-account page.

2021-06-09 - version 1.8.2
* Fixed reCaptcha JS loading even when its inactive.

2021-06-08 - version 1.8.1
* Fixed WC API error in 1.8.0.
* Fixed price disappear issue when no rule is created.
* Fixed CSS issue with user roles dropdown.

2021-05-21 - version 1.8.0
* Fixed pending user staus issue when approve user is not active.
* Added the feature of offered price increase by percentage.
* Added the field type for terms and conditions.
* Fixed the issue of offered price display including/excluding vat.
* Fixed the issue of overriding templates in child themes.
* Fixed the issue of price display in mini cart drop down.
* Fixed the issue of button display according to menu order of quote rules.
* Fixed the issue of products addons display with custom buttons.
* included the SKU of products in request a quote page.
* included the view file link in emails.
* Added shortcode for mini quote basket.
* Fixed role based pricing bug with tax exempted users.

2021-04-16 - version 1.7.2
* Fixed rule priority issue.
* Included compatibility for price including and excluding tax variables added from WooCommerce>Tax>Suffix.
* Fixed Fixed price inclusive and exclusive tax issue.

2021-04-08 - version 1.7.1
* Fixed price range issue on variable products.
* Fixed inclusive/exclusive tax display issue on table pricing.
* Fixed mini cart incorrect price display issue.

2021-03-29 - version 1.7.0
* Added compatibility for product addons.
* Compatible with prices exclusive and exclusive of tax settings and role based based pricing.
* Fixed JS issue with add rule button for multi-lingual sites.
* Fixed user status change issue on password reset.

2021-03-10 - version 1.6.4
* Fixed category prices application issue in role-based pricing.
* Added custom columns for CSV export on the product level.
* JS conflict fixed with multi-lingual site.

2021-03-03 - version 1.6.3
* Feature added: Approve/disapprove user from user edit page in admin panel.
* Fixed uploaded file access issue.
* Fixed issue of pending users filter in the admin panel > All User.
* Fixed file uplaod issue in tax exempt.

2021-03-01 - version 1.6.2
* Fixed bug with view link in admin panel.
* Fixed CSS issue in admin panel that conflicts with WPML meta boxes.
* Fixed page's content disappearing issue in product visibility.
* Improved WPML compatibility.

2021-02-18 - version 1.6.1
* Fixed issue of empty custom button restricting the quote button to display.
* Fixed inclusive/exclusive prices display by user roles.
* Fixed bug of range price in role based pricing for variable products.

2021-02-09 - version 1.6.0

* Added the compatibility of WPML
* Resolve the issue of translations

Request a Quote

* Ability to create a new quote for registered customers from the backend
* Updated request a quote with following new features and fixes.
* Show offered price on quote page, my-account quotes details, emails.
* Convert quote into order, using the offered price. (Admin/Customer).
* Option to display request a quote automatically on out of stock products.
* Keep the quote saved when user logout and login into the account again.
* Keep RFQ fields saved once filled while customer continue to shop.
* Show mini quote basket to only those user rules where RFQ is enabled.
* Create quote from back end.
* Create unlimited fields for request a quote form.
* Option to exclude any variation from request a quote rule.
* Show/Hide standard prices in quote page, my account quotes and quote emails.
* Display products price total and tax at quote page.
* Add statuses of quote status (Pending, In process, Accepted, Rejected, Cancelled, Converted to Order ).
* Emails notification for quote status changes.
* Option to override templates of emails, request a quote page, mini quote dropdown, quotes tables in emails and my-account and fields of quotes.
* Bug fixed and compatibility improvements with page builder plugins.
* Added the feature to apply discounts on regular or sale price by user roles
* Added price suffix after role based prices

Tax Display Feature

* Added the feature to display prices with and without tax by user roles

Role based pricing.

* Fixed the issue of table display for variation 
* Fixed the issue of enforce min/max quantity for add to cart and update cart
* Fixed the issue of price display for product variations
* Added the feature to apply discounts on regular or sale price by user roles

Registration fields

* Fixed the issue of fields values display in order meta and emails

Product visibility

* Optimization of solution to resolve the issue of speed.
* Compatibility with short codes of woocommerce.

2020-11-25 - version 1.5.1
* Tiered pricing issue fixed with variable products
* Some typo fixes
* Issue fixed with product visibility settings display on backend

2020-11-24 - version 1.5.0
* Major feature: Tiered pricing based on user roles
* Fixed bug of custom fields at orders detail page and order email.
* Product visibility compatibility with products loaded using shortcode
* Fixed Typo fixes
* Added translations for missing strings
* Its a major update - existing customers are requested to resave their role-based pricing rules and update products where the role based pricing has been added

2020-10-02 - version 1.4.5
* Fixed the issue of js error that prevents the loading of WooCommerce Analytics.
* Fixed issue with role based pricing in variable products.

2020-09-10 - version 1.4.4
* Fixed the issue of tax-exempt for product price inclusive of tax.
* Now tax-exemption will apply for shipping tax as well.
* Fixed the issue of tax-exempt if the expiry date is not set.
* Added the option to exempt tax for guest users.

2020-09-04 - version 1.4.3
* Fixed issue with Request a Quote rules in admin.

2020-08-28 - version 1.4.2
* Fixed enabled default fields show.

2020-08-27 - version 1.4.1
* Fixed the issue of mail to admin when user request for tax exempt.
* Added the option to make fields required.
* Fixed the issue of display expiry date and status.
* Fixed the issue of form display when none of the field is enable.
* Fixed the issue of title of fields for tax exempt in email and notification tab settings.
* Compatibility with WooCommerce 4.4.x.

2020-08-22 - version 1.4.0
* Integrated the Addify Tax Exempt extension
	1. Allow all or selected user roles to claim tax exemption
	2. Display tax exemption form in “my account”
	3. Customize form fields
	4. Display tax exemption status in “my account” – pending, rejected or approved
	5. The store admin can review tax exemption requests from the back-office users’ section.
	6. The store admin can approve/disapprove tax exemption requests
	7. Add expiry date for tax exemption
	8. Email notifications for admin when the tax exemption form is submitted
	9. Email notification for customers on approval & rejection of tax exemption requests
	10. Display tax exemption detail in admin order detail page, customers order detail page, and order email
	11. Admin can grand tax exemption status to selected customers from the back-office (no need to submit tax info)
	12. Allow guest users to claim tax exemption
	13. Customize notification messages
* Restrict Payment methods by user roles
* Restrict Shipping methods by user roles

2020-08-18 - version 1.3.0
* Removed the limitation of registration fields at checkout except for file upload
* Fixed the issue of user visibility in admin settings
* Fixed the issue of view button in All posts and products Table
* Added the WooCommerce templates to replace the add to cart with request a Quote button to resolve the issue of
	1. Product must be purchasable
	2. Product should be in stock
	3. Enabled the all Hooks of WooCommerce in the form to show the messages and Buttons by any other plugin
	4. Resolved the issue of the design of Request a Quote Button and Quantity Box
* Compatible with most of the themes, page builders and product tables plugins
* Added redirect settings for Request a Quote
* Added settings to choose multiple menu's for Quote Basket
* Added option to choose the style of Quote basket, enable/disable emails to users and enable/disable add Quote Copy in emails
* Added the WooCommerce template for Quotes emails
* Fixed issue of the field width to select the customer in role-based pricing
* Fixed the issue of translation of "Welcome to" Text in emails.
* Fields display issues fixed at my-account and checkout page
* Fixed the issues of user role dependent fields at admin edit profile
* Fix the validation errors at checkout
* Fixed the issue of translation of validation errors for registration fields.
* Added feature to include default fields in the registration form
* Fixed the issue of translation of validation errors for RFQ
* Fixed the issue of empty customer rules in Role-Based pricing
* Fixed the issue of min and max quantity for guest users in update cart (Role based pricing)
* Fixed the issue of displaying fields value of independent fields in edit account form

2020-07-28 - version 1.2.8
* Fixed the issue of errors on Quote Request page
* Fixed the issue of display of product thumbnail when variation has no image in Request a Quote form. 

2020-07-12 - version 1.2.7
* Removed the limitation where the rule based configuration wasn’t working when the product level role based pricing was configured.
* Removed the limitation where the rule based configuration wasn’t working when the product level role based pricing was configured(Applied on variable products).
* Added feature to handle multiple user roles.
* Fixed min and max quantity validation in product and rule level for role based pricing.
* Fixed the issue of price, display on shop and product pages when prices are set on product level as well as rule level.
* Fixed the issue of Min and Max Quantity for products
* Fixed the issue of role based pricing for guest users

2020-06-11 - version 1.2.6
* Approve/Disapprove links in admin email.
* Show registrations fields in admin order detail page and order email.

2020-05-06 - version 1.2.5
* Issue fixed with blog posts in products visibility.
* Fixed UTF-8 characters issue in emails.
* Give option to enable or disable admin and welcome email in registration fields.
* Give option to choose redirection mode, either custom URL or custom message in products visibility.

2020-05-01 - version 1.2.4
* Issue fixed with role based pricing when more than 5 rules are created.

2020-04-26 - version 1.2.3
* New feature added, Keep or replace add to cart button with a new custom button and add a custom link for that button.
* Fixed issue with guest user add to quote with WP Engine hosting.
* Fixed JS issue with the theme builders and some plugin in admin panel
* Issue fixed with approve new user functionality when no fields are added from this module.
* Use default email design to send registration related emails.

2020-04-07 - version 1.2.2
* Issue fixed with the product visibility for guest users.

2020-04-05 - version 1.2.1
* Issue fixed with percentage increase price for guest users.

2020-03-31 - version 1.2.0
* New feature added, Role based pricing.

2020-03-26 - version 1.1.0
* A small issue fixed with role base product visibility.

2020-02-19 - version 1.0.0
* Initial release of the plugin.

