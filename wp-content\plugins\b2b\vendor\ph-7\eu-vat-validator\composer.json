{"name": "ph-7/eu-vat-validator", "description": "A simple and clean PHP class that validates EU VAT numbers against the central ec.europa.eu database (using the official europa API).", "keywords": ["eu vat", "vat number", "validate eu vat", "validate vat number", "vat validator", "vat number validator", "vat", "tva", "tva number", "validation", "validator", "VIES"], "type": "library", "homepage": "http://ph7.me", "license": "GPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://pierrehenry.be", "role": "Software Engineer"}], "require": {"php": ">=7.1.0", "ext-soap": "*"}, "require-dev": {"phpunit/phpunit": "^7.5", "phake/phake": "^3.1"}, "autoload": {"psr-4": {"PH7\\Eu\\": "src"}}, "scripts": {"test": "vendor/bin/phpunit"}}