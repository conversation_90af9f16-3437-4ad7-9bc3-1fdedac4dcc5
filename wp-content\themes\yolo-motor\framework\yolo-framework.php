<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2016, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

// Check Visual Composer is active before load vc
function yolo_check_vc_status() {
    include_once ABSPATH.'wp-admin/includes/plugin.php';
    if( is_plugin_active('js_composer/js_composer.php') ) {
        return true;
    } else {
        return false;
    }
}
if( !function_exists( 'yolo_framework' ) ) {
	function yolo_framework() {
		
		// Load core functions
		if (file_exists( get_template_directory() . '/framework/core/_init.php')) {
		    require_once get_template_directory() . '/framework/core/_init.php';
		}

		// Load include libraries
		if (file_exists( get_template_directory() . '/framework/includes/_init.php')) {
		    require_once get_template_directory() . '/framework/includes/_init.php';
		}
		
		// Load VC extension
		if( true == yolo_check_vc_status() ) {
			require_once get_template_directory() . '/framework/vc_extension/_init.php';
		}
		
	}

	// Hook the framework initialization to the init action to ensure translations are loaded
	add_action( 'init', 'yolo_framework' );
}