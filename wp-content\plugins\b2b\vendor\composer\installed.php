<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'reference' => '093f2d9739cec57428e39ddadedfd4f3ae862c0f',
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.8.1',
            'version' => '2.8.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'reference' => 'f47dcf3c70c584de14f21143c55d9939631bc6cf',
            'dev_requirement' => false,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.5',
            'version' => '0.5.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'reference' => '671df0f3516252011aa94f9e8e3b3b66199339f8',
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.1',
            'version' => '0.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'reference' => '8a8a1ebcf6aea861ef30197999f096f7bd4b4456',
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => '8.4.0',
            'version' => '8.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'reference' => 'e41d2140031d533348b2192a83f02d8dd8a71d30',
            'dev_requirement' => false,
        ),
    ),
);
