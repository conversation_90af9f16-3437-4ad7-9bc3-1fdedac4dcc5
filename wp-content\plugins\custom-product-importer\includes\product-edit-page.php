<?php
// Add Price Code dropdowns to WooCommerce product edit page
add_action('woocommerce_product_options_general_product_data', 'add_price_code_fields');

function add_price_code_fields()
{
    global $post;

    $usd_price_codes = get_price_codes('wp_price_code_list', 'wp_price_list', $post->ID);
    $eur_price_codes = get_price_codes('wp_price_code_list_eur', 'wp_price_list_eur', $post->ID);
    $gbp_price_codes = get_price_codes('wp_price_code_list_gbp', 'wp_price_list_gbp', $post->ID);
    echo "<p class='form-field model_size_field '>";
    echo '<label>Price code (USD)</label>';
    echo '<select id="usd_price_code" name="usd_price_code">';
    foreach ($usd_price_codes as $option) {
        echo $option; // Each option includes the selected attribute if applicable
    }
    echo '</select>';
    echo "</p>";

    echo "<p class='form-field model_size_field '>";
    echo '<label>Price code (EUR)</label>';
    echo '<select id="eur_price_code" name="eur_price_code">';
    foreach ($eur_price_codes as $option) {
        echo $option; // Each option includes the selected attribute if applicable
    }
    echo '</select>';
    echo "</p>";

    echo "<p class='form-field model_size_field '>";
    echo '<label>Price code (GBP)</label>';
    echo '<select id="gbp_price_code" name="gbp_price_code">';
    foreach ($gbp_price_codes as $option) {
        echo $option; // Each option includes the selected attribute if applicable
    }
    echo '</select>';
    echo "</p>";
}

// Function to fetch price codes
function get_price_codes($price_code_table, $price_list_table, $product_id)
{
    global $wpdb;
    $product_sku = get_post_meta($product_id, '_sku', true);
    $current_price_code = $wpdb->get_var($wpdb->prepare(
        "SELECT price_code_id FROM $price_list_table WHERE product_sku = %s",
        $product_sku
    ));
    $price_codes = $wpdb->get_results("SELECT id, price_code_title FROM $price_code_table", ARRAY_A);
    $options = array('' => __('Select Price Code', 'woocommerce')); // Default option
    foreach ($price_codes as $code) {
        $options[$code['id']] = $code['price_code_title'];
    }


    if ($current_price_code) {
        if (array_key_exists($current_price_code, $options)) {
            return array_map(function ($title, $id) use ($current_price_code) {
                $selected = ($id == $current_price_code) ? ' selected' : '';
                return '<option value="' . esc_attr($id) . '"' . $selected . '>' . esc_html($title) . '</option>';
            }, $options, array_keys($options));
        }
    } else{
        return array_map(function ($title, $id)  {
            return '<option value="' . esc_attr($id) . '">' . esc_html($title) . '</option>';
        }, $options, array_keys($options));
    }

    return $options;
}

add_action('woocommerce_process_product_meta', 'save_price_code_fields');
function save_price_code_fields($post_id)
{
    global $wpdb;
    if (isset($_POST['usd_price_code']) && $_POST['usd_price_code'] != '') {
        $price_code_id = sanitize_text_field($_POST['usd_price_code']);
        $product_sku = get_post_meta($post_id, '_sku', true);
        $existing_entry = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM wp_price_list WHERE product_sku = %s",
            $product_sku
        ));

        if ($existing_entry) {
            $wpdb->update(
                'wp_price_list',
                array('price_code_id' => $price_code_id),
                array('product_sku' => $product_sku)
            );
        } else {
            $wpdb->insert(
                'wp_price_list',
                array(
                    'product_sku' => $product_sku,
                    'price_code_id' => $price_code_id
                )
            );
        }
    }

    if (isset($_POST['eur_price_code']) && $_POST['eur_price_code'] != '') {
        $price_code_id = sanitize_text_field($_POST['eur_price_code']);
        $product_sku = get_post_meta($post_id, '_sku', true);
        $existing_entry = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM wp_price_list_eur WHERE product_sku = %s",
            $product_sku
        ));

        if ($existing_entry) {
            $wpdb->update(
                'wp_price_list_eur',
                array('price_code_id' => $price_code_id),
                array('product_sku' => $product_sku)
            );
        } else {
            $wpdb->insert(
                'wp_price_list_eur',
                array(
                    'product_sku' => $product_sku,
                    'price_code_id' => $price_code_id
                )
            );
        }
    }

    if (isset($_POST['gbp_price_code']) && $_POST['gbp_price_code'] != '') {
        $price_code_id = sanitize_text_field($_POST['gbp_price_code']);
        $product_sku = get_post_meta($post_id, '_sku', true);
        $existing_entry = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM wp_price_list_gbp WHERE product_sku = %s",
            $product_sku
        ));

        if ($existing_entry) {
            $wpdb->update(
                'wp_price_list_gbp',
                array('price_code_id' => $price_code_id),
                array('product_sku' => $product_sku)
            );
        } else {
            $wpdb->insert(
                'wp_price_list_gbp',
                array(
                    'product_sku' => $product_sku,
                    'price_code_id' => $price_code_id
                )
            );
        }
    }
}
