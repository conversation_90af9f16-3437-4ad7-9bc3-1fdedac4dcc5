<?php
function fetch_product_ids() {
    global $wpdb;

    $skus = $_POST['skus']; // Get SKUs from request

    // Prepare placeholders and query
    $placeholders = implode(',', array_fill(0, count($skus), '%s'));
    $query = $wpdb->prepare(
        "SELECT post_id AS product_id, meta_value AS product_sku 
         FROM {$wpdb->postmeta} 
         WHERE meta_key = '_sku' AND meta_value IN ($placeholders)",
        $skus
    );

    // Fetch results
    $results = $wpdb->get_results($query);

    // Prepare response
    $response = [];
    foreach ($skus as $sku) {
        $product = get_result_array_filter($results, $sku);
        $response[] = [
            'product_id' => $product !== null ? $product->product_id : -1,
            'product_sku' => $sku
        ];
    }
    wp_send_json($response);
}

function get_result_array_filter($results, $sku) {
    foreach ($results as $result) {
        if($result->product_sku === $sku) {
            return $result;
        }
    }
    return null;
}
function save_stock_data() {
    global $wpdb;

    // $data = json_decode(stripslashes($_POST['data']), true); // Parse data from request

    // $product_ids = array_column($data, 'product_id');
    // $meta_keys = array_unique(array_column($data, 'meta_stock'));

    // // Step 1: Retrieve existing stock values in bulk
    // $placeholders = implode(',', array_fill(0, count($product_ids), '%d'));
    // $query = "
    //     SELECT post_id, meta_key, meta_value 
    //     FROM {$wpdb->postmeta} 
    //     WHERE post_id IN ($placeholders) AND meta_key IN ('" . implode("','", $meta_keys) . "')
    // ";
    // $existing_stock = $wpdb->get_results($wpdb->prepare($query, ...$product_ids), ARRAY_A);

    // // Organize current stock data for quick lookup
    // $stock_lookup = [];
    // foreach ($existing_stock as $row) {
    //     $stock_lookup[$row['post_id']][$row['meta_key']] = (int)$row['meta_value'];
    // }

    // // Step 2: Prepare DELETE and INSERT queries
    // $deletePlaceholders = [];
    // $deleteParams = [];
    // $insertValues = [];
    // $insertParams = [];

    // foreach ($data as $row) {
    //     $product_id = $row['product_id'];
    //     $meta_stock = $row['meta_stock'];
    //     $new_stock_count = $row['stock_count'];

    //     // Calculate updated stock count
    //     $current_stock = isset($stock_lookup[$product_id][$meta_stock]) ? $stock_lookup[$product_id][$meta_stock] : 0;
    //     $updated_stock = $current_stock + $new_stock_count;

    //     // Prepare DELETE queries
    //     $deletePlaceholders[] = "(%d, %s)";
    //     $deleteParams[] = $product_id;
    //     $deleteParams[] = $meta_stock;

    //     // Prepare INSERT queries
    //     $insertValues[] = "(%d, %s, %d)";
    //     $insertParams[] = $product_id;
    //     $insertParams[] = $meta_stock;
    //     $insertParams[] = $updated_stock;

    //     // Insert additional meta keys
    //     foreach ($row['additional_meta'] as $meta) {
    //         $deletePlaceholders[] = "(%d, %s)";
    //         $deleteParams[] = $product_id;
    //         $deleteParams[] = $meta['key'];

    //         $insertValues[] = "(%d, %s, %s)";
    //         $insertParams[] = $product_id;
    //         $insertParams[] = $meta['key'];
    //         $insertParams[] = $meta['value'];
    //     }
    // }

    // // Step 3: Execute DELETE queries
    // if (!empty($deletePlaceholders)) {
    //     $deleteQuery = "
    //         DELETE FROM {$wpdb->postmeta} 
    //         WHERE (post_id, meta_key) IN (" . implode(',', $deletePlaceholders) . ")";
    //     $wpdb->query($wpdb->prepare($deleteQuery, ...$deleteParams));
    // }

    // // Step 4: Execute INSERT queries
    // if (!empty($insertValues)) {
    //     $insertQuery = "
    //         INSERT INTO {$wpdb->postmeta} (post_id, meta_key, meta_value) 
    //         VALUES " . implode(',', $insertValues);
    //     $wpdb->query($wpdb->prepare($insertQuery, ...$insertParams));
    // }

    // wp_send_json(['status' => 'success']);
    $data = json_decode(stripslashes($_POST['data']), true); // Parse data from request

    // Prepare delete and insert queries
    $deletePlaceholders = [];
    $deleteParams = [];
    $insertValues = [];
    $insertParams = [];

    foreach ($data as $row) {
        $deletePlaceholders[] = "(%d, %s)";
        $deleteParams[] = $row['product_id'];
        $deleteParams[] = $row['meta_stock'];

        $deletePlaceholders[] = "(%d, %s)";
        $deleteParams[] = $row['product_id'];
        $deleteParams[] = "_manage_stock_{$row['meta_shuffix']}";

        $deletePlaceholders[] = "(%d, %s)";
        $deleteParams[] = $row['product_id'];
        $deleteParams[] = "_stock_status_{$row['meta_shuffix']}";

        $deletePlaceholders[] = "(%d, %s)";
        $deleteParams[] = $row['product_id'];
        $deleteParams[] = "_backorders_{$row['meta_shuffix']}";

        $deletePlaceholders[] = "(%d, %s)";
        $deleteParams[] = $row['product_id'];
        $deleteParams[] = "_low_stock_amount_{$row['meta_shuffix']}";

        $insertValues[] = "(%d, %s, %d)";
        $insertParams[] = $row['product_id'];
        $insertParams[] = $row['meta_stock'];
        $insertParams[] = $row['stock_count'];

        // Insert additional meta keys
        foreach ($row['additional_meta'] as $meta) {
            $insertValues[] = "(%d, %s, %s)";
            $insertParams[] = $row['product_id'];
            $insertParams[] = $meta['key'];
            $insertParams[] = $meta['value'];
        }
    }

    $deleteQuery = "
        DELETE FROM {$wpdb->postmeta} 
        WHERE (post_id, meta_key) IN (" . implode(',', $deletePlaceholders) . ")";
    $wpdb->query($wpdb->prepare($deleteQuery, ...$deleteParams));

    $insertQuery = "
        INSERT INTO {$wpdb->postmeta} (post_id, meta_key, meta_value) 
        VALUES " . implode(',', $insertValues);
    $wpdb->query($wpdb->prepare($insertQuery, ...$insertParams));
    wp_send_json(['status' => 'success']);
}
