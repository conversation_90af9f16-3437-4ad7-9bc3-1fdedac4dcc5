jQuery(document).ready(function ($) {
    loadCustomers();
    loadProducts();
    // Load the data using DataTable
    var table = $('#pricingTable').DataTable({
        ajax: {
            url: ajaxurl,
            type: 'POST',
            data: { action: 'get_pricing_data' }
        }
    });

    // Open modal for adding new pricing
    $('#addNewPricing').on('click', function () {
        $('#pricingModal').show();
        $('#row_id').val('');
    });

    $('#closeModal').on('click', function () {
        $('#pricingModal').hide();
    });

    // Save pricing data
    $('#pricingForm').on('submit', function (e) {
        e.preventDefault();
        var action = $('#row_id').val() ? 'edit_pricing' : 'save_pricing';
        $.post(ajaxurl, $(this).serialize() + '&action=' + action, function (response) {
            if (response.success) {
                $('#pricingModal').hide();
                table.ajax.reload();
            } else {
                alert(response.data.message);
            }
        });
    });

    // Load customers for dropdown
    function loadCustomers() {
        $.post(ajaxurl, { action: 'get_customers' }, function (customers) {
            $('#customer').html('');
            $.each(customers, function (i, customer) {
                $('#customer').append('<option value="' + customer.id + '">' + customer.name + '</option>');
            });
        });
    }

    // Load products for dropdown
    function loadProducts() {
        $.post(ajaxurl, { action: 'get_products' }, function (products) {
            $('#product').html('');
            $.each(products, function (i, product) {
                $('#product').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
        });
    }

    // Delete pricing data
    $('#pricingTable').on('click', '.deletePricing', function () {
        if (confirm('Are you sure you want to delete this entry?')) {
            var id = $(this).data('id');
            $.post(ajaxurl, { action: 'delete_pricing', id: id }, function () {
                table.ajax.reload();
            });
        }
    });

    // Edit pricing data)
    $('#pricingTable').on('click', '.editPricing', function () {
        var id = $(this).data('id');
        $.post(ajaxurl, { action: 'get_pricing_by_id', id: id }, function (response) {
            if (response.success) {
                var data = response.data;
                $('#row_id').val(data.id);
                loadCustomersById(data.customer_id);
                loadProductsById(data.product_sku);
                $('#new_price').val(data.new_price);
                $('#pricingModal').show();
            } else {
                alert(response.data.message);
            }
        });
    });

    function loadCustomersById(selectedCustomerId) {
        $.post(ajaxurl, { action: 'get_customers' }, function (customers) {
            $('#customer').html('');  // Clear the dropdown
            $.each(customers, function (i, customer) {
                if(customer.id == selectedCustomerId) {
                    $('#customer').append('<option value="' + customer.id + '" selected>' + customer.name + '</option>');
                } else {
                    $('#customer').append('<option value="' + customer.id + '">' + customer.name + '</option>');
                }
            });
        });
    }

    function loadProductsById(selectedProductSku) {
        $.post(ajaxurl, { action: 'get_products' }, function (products) {
            $('#product').html('');  // Clear the dropdown
            $.each(products, function (i, product) {
                $('#product').append('<option value="' + product.product_sku + '">' + product.product_sku + '</option>');
            });
            if (selectedProductSku) {
                $('#product').val(selectedProductSku);
            }
        });
    }
    $("[name='pricingTable_length']").css({ 'width': '100px' });
    $("#closeModal").on("click", function () {
        $('#pricingModal').hide();
    });
    $("#close").on("click", function () {
        $('#pricingModal').hide();
    });

});

