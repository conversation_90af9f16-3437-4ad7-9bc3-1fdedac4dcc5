{"name": "phenx/php-font-lib", "type": "library", "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "license": "LGPL-2.1-or-later", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "autoload-dev": {"psr-4": {"FontLib\\Tests\\": "tests/FontLib"}}, "config": {"bin-dir": "bin"}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5 || ^6"}}